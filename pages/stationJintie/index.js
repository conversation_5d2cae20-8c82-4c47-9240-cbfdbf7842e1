// 一站津贴查询主页面
Page({
  data: {
    // 用户信息
    username: '',
    role: '',
    roleText: '',

    // 页面状态
    currentTab: 'current',
    isLoading: false,
    isHistoryLoading: false,
    showAllUsers: false, // 个人用户是否查看全部

    // 当前津贴数据
    jintieRecords: [],
    currentDataInfo: {}, // 当前数据信息（月度、时间等）

    // 历史记录数据
    selectedYear: '',
    selectedDate: '', // 选中的历史日期
    yearRange: [], // 年份选择范围
    selectedYearIndex: 0, // 选中年份的索引
    historyRecords: [],

    // 修改密码相关
    showPasswordModal: false,
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    isChangingPassword: false,

    // 个人年度数据相关
    showPersonalDataModal: false,
    personalDataList: [],
    selectedPersonName: '',
    isLoadingPersonalData: false,
    currentYear: new Date().getFullYear(),
    personalDataTotal: 0
  },

  onLoad: function(options) {
    // 检查登录状态
    this.checkLoginStatus();

    // 初始化年份（用于历史记录）
    const today = new Date();
    const currentYear = today.getFullYear();

    // 生成年份选择范围：从2025年到当前年份，不包含未来年份
    const yearRange = [];
    const startYear = 2025; // 津贴系统从2025年开始
    for (let year = startYear; year <= currentYear; year++) {
      yearRange.push(year.toString());
    }

    // 计算当前年份在数组中的索引
    const currentYearIndex = yearRange.indexOf(currentYear.toString());

    this.setData({
      selectedYear: currentYear.toString(),
      yearRange: yearRange,
      selectedYearIndex: currentYearIndex >= 0 ? currentYearIndex : yearRange.length - 1
    });

    // 加载最新津贴数据
    this.loadLatestJintieData();
  },

  onShow: function() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');
    const role = wx.getStorageSync('stationRole');

    if (!token || !username) {
      // 未登录，跳转到登录页面
      wx.redirectTo({
        url: '/pages/stationJintie/login'
      });
      return false;
    }
    
    // 设置用户信息
    const roleTexts = {
      'admin': '系统管理员',
      'station_staff': '一站人员',
      'manager': '普通管理员',
      'user': '普通用户'
    };

    this.setData({
      username: username,
      role: role,
      roleText: roleTexts[role] || '普通用户'
    });

    return true;
  },

  // 格式化日期
  formatDate: function(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  // 切换标签页
  switchTab: function(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab
    });

    if (tab === 'history' && this.data.historyRecords.length === 0) {
      this.loadHistoryData();
    }
  },

  // 切换查看模式（个人/全部）
  toggleViewAll: function() {
    const showAll = !this.data.showAllUsers;
    this.setData({
      showAllUsers: showAll
    });
    this.loadLatestJintieData();
  },

  // 点击姓名显示个人年度数据
  onNameClick: function(e) {
    const name = e.currentTarget.dataset.name;
    if (!name) return;

    this.setData({
      selectedPersonName: name,
      showPersonalDataModal: true,
      personalDataList: [],
      isLoadingPersonalData: true
    });

    this.loadPersonalYearData(name);
  },

  // 加载个人年度数据
  loadPersonalYearData: function(name) {
    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');

    if (!token || !username) {
      this.checkLoginStatus();
      return;
    }

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: {
        action: 'get_personal_yearly_data',
        token: token,
        username: username,
        person_name: name,
        year: this.data.currentYear
      },
      success: (res) => {
        if (res.data.status === 'success') {
          const records = res.data.data.records || [];
          // 计算合计金额
          let total = 0;
          records.forEach(record => {
            // 移除金额中的逗号并转换为数字
            const amount = parseFloat(record.amount.replace(/,/g, '')) || 0;
            total += amount;
          });

          this.setData({
            personalDataList: records,
            personalDataTotal: total.toLocaleString()
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取个人数据失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoadingPersonalData: false });
      }
    });
  },

  // 关闭个人数据模态框
  hidePersonalDataModal: function() {
    this.setData({
      showPersonalDataModal: false,
      personalDataList: [],
      selectedPersonName: '',
      isLoadingPersonalData: false,
      personalDataTotal: 0
    });
  },

  // 查看备注内容
  viewRemark: function(e) {
    const remark = e.currentTarget.dataset.remark;
    if (remark && remark !== '-') {
      wx.showModal({
        title: '备注内容',
        content: remark,
        showCancel: false,
        confirmText: '确定'
      });
    }
  },

  // 跳转到用户管理页面
  goToUserManagement: function() {
    wx.navigateTo({
      url: '/pages/stationJintie/userManagement'
    });
  },

  // 显示修改密码模态框
  showChangePassword: function() {
    this.setData({
      showPasswordModal: true,
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  },

  // 隐藏修改密码模态框
  hideChangePassword: function() {
    this.setData({
      showPasswordModal: false,
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    });
  },

  // 阻止事件冒泡
  stopPropagation: function() {
    // 空函数，用于阻止事件冒泡
  },

  // 密码输入事件
  onOldPasswordInput: function(e) {
    this.setData({ oldPassword: e.detail.value });
  },

  onNewPasswordInput: function(e) {
    this.setData({ newPassword: e.detail.value });
  },

  onConfirmPasswordInput: function(e) {
    this.setData({ confirmPassword: e.detail.value });
  },

  // 处理修改密码
  handleChangePassword: function() {
    const { oldPassword, newPassword, confirmPassword } = this.data;

    // 验证输入
    if (!oldPassword || !newPassword || !confirmPassword) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    if (newPassword.length < 6) {
      wx.showToast({
        title: '新密码长度不能少于6位',
        icon: 'none'
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      wx.showToast({
        title: '两次输入的新密码不一致',
        icon: 'none'
      });
      return;
    }

    this.setData({ isChangingPassword: true });

    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: {
        action: 'change_password',
        token: token,
        username: username,
        old_password: oldPassword,
        new_password: newPassword
      },
      success: (res) => {
        if (res.data.status === 'success') {
          wx.showToast({
            title: '密码修改成功',
            icon: 'success'
          });
          this.hideChangePassword();
        } else {
          wx.showToast({
            title: res.data.message || '密码修改失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isChangingPassword: false });
      }
    });
  },

  // 年份选择
  onYearChange: function(e) {
    const selectedIndex = e.detail.value;
    const selectedYear = this.data.yearRange[selectedIndex];
    this.setData({
      selectedYear: selectedYear,
      selectedYearIndex: selectedIndex
    });
    this.loadHistoryData();
  },

  // 加载最新或指定日期的津贴数据
  loadLatestJintieData: function(date) {
    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');

    if (!token || !username) {
      this.checkLoginStatus();
      return;
    }

    this.setData({ isLoading: true });

    // 根据用户角色和查看模式决定查询参数
    const { role, showAllUsers } = this.data;
    const isAdmin = role === 'admin' || role === 'manager';
    const queryAllUsers = isAdmin || showAllUsers;

    let requestData = {
      action: 'get_latest_jintie_data',
      token: token,
      username: username,
      view_all: queryAllUsers
    };
    if (date) {
      requestData['date'] = date; // 增加日期参数
    }

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: requestData,
      success: (res) => {
        if (res.data.status === 'success') {
          const data = res.data.data;
          this.setData({
            jintieRecords: data.records || [],
            currentDataInfo: {
              record_date: data.record_date,
              month_year: data.month_year
            }
          });
        } else {
          if (res.data.message.includes('未登录') || res.data.message.includes('token无效')) {
            // token失效，重新登录
            this.handleLogout();
          } else {
            wx.showToast({
              title: res.data.message || '获取数据失败',
              icon: 'none'
            });
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 一键复制功能
  copyJintieData: function() {
    const { currentDataInfo, jintieRecords } = this.data;
    
    if (!currentDataInfo.month_year || jintieRecords.length === 0) {
      wx.showToast({
        title: '暂无数据可复制',
        icon: 'none'
      });
      return;
    }
    
    // 构建复制内容
    let copyContent = `数据月度：${currentDataInfo.month_year}\n`;
    copyContent += `记录时间：${currentDataInfo.record_date}\n\n`;
    
    // 添加每个人的信息
    jintieRecords.forEach((item, index) => {
      copyContent += `${item.name}：${item.total_amount}\n`;
    });
    
    // 复制到剪贴板
    wx.setClipboardData({
      data: copyContent,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  // 加载历史数据
  loadHistoryData: function() {
    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');
    
    if (!token || !username) {
      this.checkLoginStatus();
      return;
    }
    
    this.setData({ isHistoryLoading: true });
    
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: {
        action: 'get_jintie_history',
        token: token,
        username: username,
        year: this.data.selectedYear
      },
      success: (res) => {
        if (res.data.status === 'success') {
          this.setData({
            historyRecords: res.data.data.records || []
          });
        } else {
          if (res.data.message.includes('未登录') || res.data.message.includes('token无效')) {
            // token失效，重新登录
            this.handleLogout();
          } else {
            wx.showToast({
              title: res.data.message || '获取历史数据失败',
              icon: 'none'
            });
          }
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isHistoryLoading: false });
      }
    });
  },

  // 查看历史详情
  viewHistoryDetail: function(e) {
    const date = e.currentTarget.dataset.date;
    this.setData({
      selectedDate: date,
      currentTab: 'current'
    });
    this.loadLatestJintieData(date); // 传递选中的日期
  },

  // 退出登录
  handleLogout: function() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const username = wx.getStorageSync('stationUsername');

          // 调用后端API记录退出操作
          wx.request({
            url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
            method: 'POST',
            data: {
              action: 'logout',
              username: username
            },
            success: (res) => {
              // 退出日志记录成功
            },
            fail: (err) => {
              // 退出日志记录失败
            },
            complete: () => {
              // 无论API调用成功与否，都执行退出操作
              // 清除登录信息
              wx.removeStorageSync('stationToken');
              wx.removeStorageSync('stationUsername');
              wx.removeStorageSync('stationRole');
              wx.removeStorageSync('stationUserId');
              wx.removeStorageSync('stationExpireTime');

              wx.showToast({
                title: '已退出登录',
                icon: 'success'
              });

              // 跳转到登录页面
              setTimeout(() => {
                wx.redirectTo({
                  url: '/pages/stationJintie/login'
                });
              }, 1500);
            }
          });
        }
      }
    });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    if (this.data.currentTab === 'current') {
      this.loadLatestJintieData();
    } else {
      this.loadHistoryData();
    }

    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '一站津贴查询',
      path: '/pages/stationJintie/login'
    };
  }
});
