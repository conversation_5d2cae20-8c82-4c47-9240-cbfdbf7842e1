/* 用户管理页面样式 */
.container {
  min-height: 100vh;
  background: #f7f9fc;
  padding-bottom: 40rpx;
}

/* 头部样式 */
.header {
  background: #fff;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border-bottom: 1rpx solid #f0f0f0;
  min-height: 60rpx;
}

.header-title {
  flex: 2;
  text-align: center;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.header-left, .header-right {
  flex: 1;
  display: flex;
  align-items: center;
}

.back-btn {
  display: flex;
  align-items: center;
  padding: 6rpx 10rpx;
  border-radius: 4rpx;
  background: transparent;
  border: none;
  transition: all 0.3s ease;
}

.back-btn:active {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(0.95);
}

.add-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  background: #2196F3;
  border-color: #2196F3;
  margin-left: auto;
  width: fit-content;
  box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
  transition: all 0.3s ease;
}

.add-btn:active {
  background: #1976D2;
  transform: scale(0.95);
}

.back-icon, .add-icon {
  font-size: 20rpx;
  margin-right: 4rpx;
}

.back-text, .add-text {
  font-size: 22rpx;
  color: #666;
}

.add-btn .add-text {
  color: white;
}

.add-btn .add-icon {
  color: white;
}

/* 搜索框样式 */
.search-section {
  padding: 20rpx;
  background: #f7f9fc;
}

.search-box {
  position: relative;
  background: white;
  border-radius: 8rpx;
  border: 1rpx solid #e9ecef;
  overflow: hidden;
}

.search-input {
  width: 100%;
  height: 70rpx;
  padding: 0 50rpx 0 20rpx;
  font-size: 26rpx;
  border: none;
  outline: none;
  box-sizing: border-box;
}

.search-icon, .clear-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #999;
}

.clear-icon {
  color: #666;
  font-size: 32rpx;
  cursor: pointer;
}

/* 用户列表样式 */
.user-list {
  padding: 20rpx;
}

.user-item {
  background: white;
  margin-bottom: 16rpx;
  padding: 24rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid #f0f0f0;
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

.user-details {
  flex: 1;
  min-width: 0;
  margin-right: 20rpx;
}

.user-name-row {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
  gap: 12rpx;
}

.username {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  word-break: break-all;
}

.user-role {
  font-size: 20rpx;
  color: #666;
  background: #f0f0f0;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  flex-shrink: 0;
}

.user-status {
  font-size: 20rpx;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  display: inline-block;
}

.user-actions {
  display: flex;
  gap: 3rpx;
  flex-shrink: 0;
}

.action-btn {
  padding: 6rpx 12rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
  text-align: center;
  display: inline-block;
  min-width: 50rpx;
}

.edit-btn {
  background: #2196F3;
  color: white;
}

.reset-btn {
  background: #ff9500;
  color: white;
}

.delete-btn {
  background: #ff4757;
  color: white;
}

.action-btn:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* 加载和无数据样式 */
.loading {
  text-align: center;
  padding: 80rpx 0;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

.no-data {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  margin: 20rpx;
  border: 1rpx solid #f0f0f0;
}

.no-data-icon {
  display: block;
  font-size: 60rpx;
  margin-bottom: 20rpx;
  opacity: 0.4;
}

.no-data-text {
  display: block;
  font-size: 26rpx;
  color: #666;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 40rpx;
  box-sizing: border-box;
}

.modal-content {
  background: white;
  border-radius: 12rpx;
  width: 100%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
}

.modal-body {
  padding: 30rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.form-group {
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  font-size: 26rpx;
  color: #333;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 6rpx;
  font-size: 26rpx;
  background: #fff;
  box-sizing: border-box;
}

.form-input:focus {
  border-color: #2196F3;
  outline: none;
}

.form-input[disabled] {
  background: #f8f9fa;
  color: #999;
}

.picker-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80rpx;
  padding: 0 20rpx;
  border: 1rpx solid #e9ecef;
  border-radius: 6rpx;
  background: #fff;
  font-size: 26rpx;
}

.picker-arrow {
  font-size: 20rpx;
  color: #999;
}

.checkbox-group {
  margin-top: 12rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  font-size: 26rpx;
}

.checkbox-text {
  margin-left: 12rpx;
  color: #333;
}

.modal-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 6rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}

.confirm-btn {
  background: #2196F3;
  color: white;
}

.modal-btn:active {
  transform: scale(0.98);
}

.modal-btn[disabled] {
  opacity: 0.6;
  transform: none !important;
}
