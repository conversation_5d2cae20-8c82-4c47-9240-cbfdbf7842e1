// 用户管理页面
Page({
  data: {
    userList: [],
    filteredUserList: [],
    isLoading: false,
    showUserModal: false,
    isEditMode: false,
    isSaving: false,
    currentUserId: null,
    searchKeyword: '',
    
    // 表单数据
    formData: {
      username: '',
      password: '',
      role: 'user',
      roleIndex: 0,
      is_station_staff: false
    },
    
    // 角色选项（一站人员身份是独立的，不是角色）
    roleOptions: [
      { value: 'user', text: '普通用户' },
      { value: 'manager', text: '普通管理员' },
      { value: 'admin', text: '系统管理员' }
    ]
  },

  onLoad: function() {
    this.checkPermission();
    this.loadUserList();
  },

  // 检查权限
  checkPermission: function() {
    const role = wx.getStorageSync('stationRole');
    if (role !== 'admin' && role !== 'manager') {
      wx.showToast({
        title: '权限不足',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 返回上一页
  goBack: function() {
    wx.navigateBack();
  },

  // 加载用户列表
  loadUserList: function() {
    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');

    if (!token || !username) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({ isLoading: true });

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: {
        action: 'get_user_list',
        token: token,
        username: username
      },
      success: (res) => {
        if (res.data.status === 'success') {
          // 添加角色文本
          const userList = res.data.data.users.map(user => ({
            ...user,
            role_text: this.getRoleText(user.role)
          }));
          this.setData({ userList });
          this.filterUsers(); // 应用搜索过滤
        } else {
          wx.showToast({
            title: res.data.message || '获取用户列表失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 获取角色文本
  getRoleText: function(role) {
    const roleMap = {
      'user': '普通用户',
      'manager': '普通管理员',
      'admin': '系统管理员'
    };
    return roleMap[role] || '普通用户';
  },

  // 显示新增用户模态框
  showAddUserModal: function() {
    this.setData({
      showUserModal: true,
      isEditMode: false,
      currentUserId: null,
      formData: {
        username: '',
        password: '',
        role: 'user',
        roleIndex: 0,
        is_station_staff: false
      }
    });
  },

  // 编辑用户
  editUser: function(e) {
    const user = e.currentTarget.dataset.user;
    const roleIndex = this.data.roleOptions.findIndex(option => option.value === user.role);
    
    this.setData({
      showUserModal: true,
      isEditMode: true,
      currentUserId: user.id,
      formData: {
        username: user.username,
        password: '',
        role: user.role,
        roleIndex: roleIndex >= 0 ? roleIndex : 0,
        is_station_staff: user.is_station_staff == 1
      }
    });
  },

  // 重置密码
  resetPassword: function(e) {
    const userId = e.currentTarget.dataset.id;
    const username = e.currentTarget.dataset.username;

    wx.showModal({
      title: '确认重置密码',
      content: `确定要将用户"${username}"的密码重置为"000000"吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performResetPassword(userId, username);
        }
      }
    });
  },

  // 执行重置密码
  performResetPassword: function(userId, username) {
    const token = wx.getStorageSync('stationToken');
    const currentUsername = wx.getStorageSync('stationUsername');

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: {
        action: 'reset_user_password',
        token: token,
        username: currentUsername,
        user_id: userId
      },
      success: (res) => {
        if (res.data.status === 'success') {
          wx.showToast({
            title: '密码重置成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: res.data.message || '重置失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 删除用户
  deleteUser: function(e) {
    const userId = e.currentTarget.dataset.id;
    const username = e.currentTarget.dataset.username;

    wx.showModal({
      title: '确认删除',
      content: `确定要删除用户"${username}"吗？此操作不可恢复。`,
      success: (res) => {
        if (res.confirm) {
          this.performDeleteUser(userId);
        }
      }
    });
  },

  // 执行删除用户
  performDeleteUser: function(userId) {
    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: {
        action: 'delete_user',
        token: token,
        username: username,
        user_id: userId
      },
      success: (res) => {
        if (res.data.status === 'success') {
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          });
          this.loadUserList(); // 重新加载列表
        } else {
          wx.showToast({
            title: res.data.message || '删除失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 隐藏用户模态框
  hideUserModal: function() {
    this.setData({ showUserModal: false });
  },

  // 阻止事件冒泡
  stopPropagation: function() {},

  // 用户名输入
  onUsernameInput: function(e) {
    this.setData({
      'formData.username': e.detail.value
    });
  },

  // 密码输入
  onPasswordInput: function(e) {
    this.setData({
      'formData.password': e.detail.value
    });
  },

  // 角色选择
  onRoleChange: function(e) {
    const index = e.detail.value;
    this.setData({
      'formData.roleIndex': index,
      'formData.role': this.data.roleOptions[index].value
    });
  },

  // 一站人员选择
  onStationStaffChange: function(e) {
    const values = e.detail.value;
    this.setData({
      'formData.is_station_staff': values.includes('station_staff')
    });
  },

  // 保存用户
  saveUser: function() {
    const { formData, isEditMode, currentUserId } = this.data;

    // 表单验证
    if (!formData.username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      });
      return;
    }

    if (!isEditMode && !formData.password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      });
      return;
    }

    this.setData({ isSaving: true });

    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');

    const requestData = {
      action: isEditMode ? 'update_user' : 'create_user',
      token: token,
      username: username,
      user_data: {
        username: formData.username.trim(),
        role: formData.role,
        is_station_staff: formData.is_station_staff ? 1 : 0
      }
    };

    if (isEditMode) {
      requestData.user_id = currentUserId;
    } else {
      requestData.user_data.password = formData.password.trim();
    }

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: requestData,
      success: (res) => {
        if (res.data.status === 'success') {
          wx.showToast({
            title: isEditMode ? '更新成功' : '创建成功',
            icon: 'success'
          });
          this.hideUserModal();
          this.loadUserList(); // 重新加载列表
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isSaving: false });
      }
    });
  },

  // 搜索输入
  onSearchInput: function(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
    this.filterUsers();
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: ''
    });
    this.filterUsers();
  },

  // 过滤用户列表
  filterUsers: function() {
    const { userList, searchKeyword } = this.data;
    let filteredList = userList;

    if (searchKeyword.trim()) {
      filteredList = userList.filter(user =>
        user.username.toLowerCase().includes(searchKeyword.toLowerCase())
      );
    }

    this.setData({
      filteredUserList: filteredList
    });
  }
});
