<!--一站津贴查询主页面-->
<view class="container">
  <!-- 头部用户信息 -->
  <view class="header">
    <view class="user-info">
      <view class="user-details">
        <view class="username-container">
          <text class="username-label">当前用户</text>
          <text class="username">{{username}}</text>
        </view>
        <view class="role-container">
          <text class="role-badge">{{roleText}}</text>
        </view>
      </view>
    </view>
    <view class="header-actions">
      <view class="action-btn" wx:if="{{role === 'admin' || role === 'manager'}}" bindtap="goToUserManagement">
        <text class="action-icon">👥</text>
        <text class="action-text">用户管理</text>
      </view>
      <view class="action-btn" bindtap="showChangePassword">
        <text class="action-icon">🔑</text>
        <text class="action-text">修改密码</text>
      </view>
      <view class="action-btn" bindtap="handleLogout">
        <text class="action-icon">🚪</text>
        <text class="action-text">退出登录</text>
      </view>
    </view>
  </view>

  <!-- 功能导航 -->
  <view class="nav-section">
    <view class="nav-item {{currentTab === 'current' ? 'active' : ''}}" bindtap="switchTab" data-tab="current">
      <text class="nav-text">当前津贴</text>
    </view>
    <view class="nav-item {{currentTab === 'history' ? 'active' : ''}}" bindtap="switchTab" data-tab="history">
      <text class="nav-text">历史记录</text>
    </view>
  </view>

  <!-- 当前津贴页面 -->
  <view class="content" wx:if="{{currentTab === 'current'}}">
    <!-- 数据信息显示 -->
    <view class="data-info" wx:if="{{currentDataInfo.record_date}}">
      <view class="info-item">
        <text class="info-label">数据月度：</text>
        <text class="info-value">{{currentDataInfo.month_year}}</text>
      </view>
      <view class="info-item">
        <text class="info-label">记录时间：</text>
        <text class="info-value">{{currentDataInfo.record_date}}</text>
      </view>
    </view>

    <!-- 一键复制按钮 -->
    <view class="copy-section" wx:if="{{jintieRecords.length > 0}}">
      <button class="copy-btn" bindtap="copyJintieData">一键复制</button>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{isLoading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 津贴数据表格 -->
    <view class="jintie-table" wx:if="{{jintieRecords.length > 0}}">
      <view class="table-header">
        <view class="table-cell header-cell">序号</view>
        <view class="table-cell header-cell">姓名</view>
        <view class="table-cell header-cell">总金额</view>
        <view class="table-cell header-cell">备注</view>
      </view>
      <view class="table-row" wx:for="{{jintieRecords}}" wx:key="id">
        <view class="table-cell">{{index + 1}}</view>
        <view class="table-cell name-cell" bindtap="onNameClick" data-name="{{item.name}}">{{item.name}}</view>
        <view class="table-cell amount">¥{{item.total_amount}}</view>
        <view class="table-cell remark">
          <text wx:if="{{item.remark && item.remark !== '-'}}" class="remark-view-btn" bindtap="viewRemark" data-remark="{{item.remark}}">查看</text>
          <text wx:else>-</text>
        </view>
      </view>
    </view>

    <!-- 个人用户查看全部/收起按钮 -->
    <view class="view-all-section" wx:if="{{role !== 'admin' && role !== 'manager'}}">
      <button class="view-all-btn" wx:if="{{!showAllUsers}}" bindtap="toggleViewAll">查看全部人员</button>
      <button class="view-all-btn secondary" wx:if="{{showAllUsers}}" bindtap="toggleViewAll">只看自己</button>
    </view>

    <!-- 无数据提示 -->
    <view class="no-data" wx:if="{{jintieRecords.length === 0 && !isLoading}}">
      <text class="no-data-icon">📋</text>
      <text class="no-data-text">暂无津贴数据</text>
      <text class="no-data-tip">请查看历史记录或联系管理员</text>
    </view>
  </view>

  <!-- 历史记录页面 -->
  <view class="content" wx:if="{{currentTab === 'history'}}">
    <!-- 年份选择 -->
    <view class="year-selector">
      <view class="year-label">查询年份：</view>
      <picker mode="selector" range="{{yearRange}}" value="{{selectedYearIndex}}" bindchange="onYearChange">
        <view class="year-picker">
          <text>{{selectedYear}}年</text>
          <text class="picker-arrow">📅</text>
        </view>
      </picker>
    </view>

    <!-- 加载状态 -->
    <view class="loading" wx:if="{{isHistoryLoading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 历史记录列表 -->
    <view class="history-list" wx:elif="{{historyRecords.length > 0}}">
      <view class="history-item" wx:for="{{historyRecords}}" wx:key="record_date" bindtap="viewHistoryDetail" data-date="{{item.record_date}}">
        <view class="history-date">
          <text class="date">{{item.record_date}}</text>
          <text class="month">{{item.month_year}}</text>
        </view>
        <view class="history-info">
          <view class="record-count">{{item.record_count}}条记录</view>
          <view class="total-amount">¥{{item.total_amount}}</view>
        </view>
        <view class="history-arrow">›</view>
      </view>
    </view>

    <!-- 无历史数据 -->
    <view class="no-data" wx:else>
      <text class="no-data-icon">📊</text>
      <text class="no-data-text">暂无{{selectedYear}}年的历史记录</text>
      <text class="no-data-tip">请选择其他年份查询</text>
    </view>
  </view>

  <!-- 修改密码模态框 -->
  <view class="modal-overlay" wx:if="{{showPasswordModal}}" bindtap="hideChangePassword">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">修改密码</text>
        <view class="modal-close" bindtap="hideChangePassword">✕</view>
      </view>
      <view class="modal-body">
        <view class="input-group">
          <text class="input-label">当前密码</text>
          <input
            class="modal-input"
            type="password"
            placeholder="请输入当前密码"
            value="{{oldPassword}}"
            bindinput="onOldPasswordInput"
          />
        </view>
        <view class="input-group">
          <text class="input-label">新密码</text>
          <input
            class="modal-input"
            type="password"
            placeholder="请输入新密码"
            value="{{newPassword}}"
            bindinput="onNewPasswordInput"
          />
        </view>
        <view class="input-group">
          <text class="input-label">确认新密码</text>
          <input
            class="modal-input"
            type="password"
            placeholder="请再次输入新密码"
            value="{{confirmPassword}}"
            bindinput="onConfirmPasswordInput"
          />
        </view>
      </view>
      <view class="modal-footer">
        <button class="modal-btn cancel" bindtap="hideChangePassword">取消</button>
        <button class="modal-btn confirm" bindtap="handleChangePassword" disabled="{{isChangingPassword}}">
          {{isChangingPassword ? '修改中...' : '确认修改'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 个人年度数据模态框 -->
  <view class="modal-overlay" wx:if="{{showPersonalDataModal}}" bindtap="hidePersonalDataModal">
    <view class="modal-content personal-data-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">{{selectedPersonName}} - {{currentYear}}年度津贴记录</text>
        <view class="modal-close" bindtap="hidePersonalDataModal">✕</view>
      </view>
      <view class="modal-body">
        <!-- 加载状态 -->
        <view class="loading" wx:if="{{isLoadingPersonalData}}">
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 个人数据列表 -->
        <view class="personal-data-list" wx:elif="{{personalDataList.length > 0}}">
          <!-- 表头 -->
          <view class="personal-data-header">
            <view class="data-header-cell">月度</view>
            <view class="data-header-cell">日期</view>
            <view class="data-header-cell">金额</view>
            <view class="data-header-cell">备注</view>
          </view>
          <!-- 数据行 -->
          <view class="personal-data-item" wx:for="{{personalDataList}}" wx:key="date">
            <view class="data-date">{{item.month}}月</view>
            <view class="data-record-date">{{item.date}}</view>
            <view class="data-amount">¥{{item.amount}}</view>
            <view class="data-remark">
              <text wx:if="{{item.remark && item.remark !== '-'}}" class="remark-view-btn" bindtap="viewRemark" data-remark="{{item.remark}}">查看</text>
              <text wx:else>-</text>
            </view>
          </view>
          <!-- 合计行 -->
          <view class="personal-data-total">
            <view class="data-total-cell">合计</view>
            <view class="data-total-cell">{{personalDataList.length}}条记录</view>
            <view class="data-total-amount">¥{{personalDataTotal}}</view>
            <view class="data-total-cell">-</view>
          </view>
        </view>

        <!-- 无数据提示 -->
        <view class="no-data" wx:else>
          <text class="no-data-icon">📊</text>
          <text class="no-data-text">暂无{{currentYear}}年度记录</text>
        </view>
      </view>
    </view>
  </view>
</view>
