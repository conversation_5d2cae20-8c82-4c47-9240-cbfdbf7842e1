<!--用户管理页面-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <view class="header-left">
      <view class="back-btn" bindtap="goBack">
        <text class="back-icon">←</text>
        <text class="back-text">返回</text>
      </view>
    </view>
    <view class="header-title">用户管理</view>
    <view class="header-right">
      <view class="add-btn" bindtap="showAddUserModal">
        <text class="add-icon">+</text>
        <text class="add-text">新增</text>
      </view>
    </view>
  </view>

  <!-- 搜索框 -->
  <view class="search-section">
    <view class="search-box">
      <input class="search-input" placeholder="搜索用户名" value="{{searchKeyword}}" bindinput="onSearchInput" />
      <view class="search-icon" wx:if="{{!searchKeyword}}">🔍</view>
      <view class="clear-icon" wx:if="{{searchKeyword}}" bindtap="clearSearch">×</view>
    </view>
  </view>

  <!-- 用户列表 -->
  <view class="user-list">
    <view class="user-item" wx:for="{{filteredUserList}}" wx:key="id">
      <view class="user-info">
        <view class="user-details">
          <view class="user-name-row">
            <text class="username">{{item.username}}</text>
            <text class="user-role">{{item.role_text}}</text>
          </view>
          <view class="user-status" wx:if="{{item.is_station_staff}}">一站人员</view>
        </view>
        <view class="user-actions">
          <view class="action-btn edit-btn" bindtap="editUser" data-user="{{item}}">编辑</view>
          <view class="action-btn reset-btn" bindtap="resetPassword" data-id="{{item.id}}" data-username="{{item.username}}">重置</view>
          <view class="action-btn delete-btn" bindtap="deleteUser" data-id="{{item.id}}" data-username="{{item.username}}">删除</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 无数据提示 -->
  <view class="no-data" wx:if="{{filteredUserList.length === 0 && !isLoading}}">
    <text class="no-data-icon">👥</text>
    <text class="no-data-text">{{searchKeyword ? '未找到匹配的用户' : '暂无用户数据'}}</text>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{isLoading}}">
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 新增/编辑用户模态框 -->
<view class="modal-overlay" wx:if="{{showUserModal}}" bindtap="hideUserModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">{{isEditMode ? '编辑用户' : '新增用户'}}</text>
      <text class="modal-close" bindtap="hideUserModal">×</text>
    </view>
    <view class="modal-body">
      <view class="form-group">
        <text class="form-label">用户名</text>
        <input class="form-input" placeholder="请输入用户名" value="{{formData.username}}" bindinput="onUsernameInput" disabled="{{isEditMode}}" />
      </view>
      <view class="form-group" wx:if="{{!isEditMode}}">
        <text class="form-label">密码</text>
        <input class="form-input" placeholder="请输入密码" value="{{formData.password}}" bindinput="onPasswordInput" password />
      </view>
      <view class="form-group">
        <text class="form-label">角色</text>
        <picker mode="selector" range="{{roleOptions}}" range-key="text" value="{{formData.roleIndex}}" bindchange="onRoleChange">
          <view class="picker-display">
            <text>{{roleOptions[formData.roleIndex].text}}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      <view class="form-group">
        <view class="checkbox-group">
          <checkbox-group bindchange="onStationStaffChange">
            <label class="checkbox-item">
              <checkbox value="station_staff" checked="{{formData.is_station_staff}}" />
              <text class="checkbox-text">一站人员</text>
            </label>
          </checkbox-group>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="modal-btn cancel-btn" bindtap="hideUserModal">取消</button>
      <button class="modal-btn confirm-btn" bindtap="saveUser" disabled="{{isSaving}}">
        {{isSaving ? '保存中...' : '保存'}}
      </button>
    </view>
  </view>
</view>
