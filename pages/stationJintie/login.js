// 一站津贴查询登录页面
Page({
  data: {
    username: '',
    password: '',
    showPassword: false,
    isLoading: false,
    rememberUsername: false,
    rememberPassword: false
  },

  onLoad: function(options) {
    // 检查是否已经登录
    this.checkLoginStatus();
    // 加载记住的账号和密码
    this.loadRememberedCredentials();
  },

  onShow: function() {
    // 每次显示页面时检查登录状态
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    const token = wx.getStorageSync('stationToken');
    const username = wx.getStorageSync('stationUsername');

    if (token && username) {
      // 已登录，直接跳转到查询页面
      wx.redirectTo({
        url: '/pages/stationJintie/index'
      });
    }
  },

  // 加载记住的账号和密码
  loadRememberedCredentials: function() {
    const rememberUsername = wx.getStorageSync('rememberUsername') || false;
    const rememberPassword = wx.getStorageSync('rememberPassword') || false;
    const savedUsername = wx.getStorageSync('savedUsername') || '';
    const savedPassword = wx.getStorageSync('savedPassword') || '';

    this.setData({
      rememberUsername: rememberUsername,
      rememberPassword: rememberPassword,
      username: rememberUsername ? savedUsername : '',
      password: rememberPassword ? savedPassword : ''
    });
  },

  // 切换记住账号
  toggleRememberUsername: function() {
    const newValue = !this.data.rememberUsername;
    this.setData({
      rememberUsername: newValue
    });

    // 如果取消记住账号，也要取消记住密码，并清除保存的数据
    if (!newValue) {
      this.setData({
        rememberPassword: false
      });
      wx.removeStorageSync('savedUsername');
      wx.removeStorageSync('savedPassword');
    }
  },

  // 切换记住密码
  toggleRememberPassword: function() {
    const newValue = !this.data.rememberPassword;

    // 如果勾选记住密码，自动勾选记住账号
    if (newValue) {
      this.setData({
        rememberUsername: true,
        rememberPassword: true
      });
    } else {
      this.setData({
        rememberPassword: false
      });
      // 如果取消记住密码，清除保存的密码
      wx.removeStorageSync('savedPassword');
    }
  },

  // 用户名输入
  onUsernameInput: function(e) {
    this.setData({
      username: e.detail.value.trim()
    });
  },

  // 密码输入
  onPasswordInput: function(e) {
    this.setData({
      password: e.detail.value
    });
  },

  // 切换密码显示
  togglePassword: function() {
    this.setData({
      showPassword: !this.data.showPassword
    });
  },

  // 处理登录
  handleLogin: function() {
    const { username, password } = this.data;

    // 基础验证
    if (!username || !password) {
      wx.showToast({
        title: '请输入用户名和密码',
        icon: 'none'
      });
      return;
    }

    if (username.length < 2) {
      wx.showToast({
        title: '用户名长度不能少于2位',
        icon: 'none'
      });
      return;
    }

    if (password.length < 6) {
      wx.showToast({
        title: '密码长度不能少于6位',
        icon: 'none'
      });
      return;
    }

    this.setData({ isLoading: true });

    // 调用登录API
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/jintie_query_api.php',
      method: 'POST',
      data: {
        action: 'station_login',
        username: username,
        password: password
      },
      success: (res) => {

        if (res.data.status === 'success') {
          // 登录成功，保存用户信息
          const userData = res.data.data;

          wx.setStorageSync('stationToken', userData.token);
          wx.setStorageSync('stationUsername', userData.username);
          wx.setStorageSync('stationRole', userData.role);
          wx.setStorageSync('stationUserId', userData.user_id);
          wx.setStorageSync('stationExpireTime', userData.expire_time);

          // 根据用户选择保存账号和密码
          this.saveCredentialsIfNeeded();

          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });

          // 跳转到查询页面
          setTimeout(() => {
            wx.redirectTo({
              url: '/pages/stationJintie/index'
            });
          }, 1500);

        } else {
          // 登录失败
          wx.showModal({
            title: '登录失败',
            content: res.data.message || '用户名或密码错误',
            showCancel: false,
            confirmText: '确定'
          });
        }
      },
      fail: (err) => {
        console.error('登录请求失败:', err);
        wx.showModal({
          title: '网络错误',
          content: '无法连接到服务器，请检查网络连接',
          showCancel: false,
          confirmText: '确定'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 保存账号和密码（如果用户选择了记住）
  saveCredentialsIfNeeded: function() {
    const { username, password, rememberUsername, rememberPassword } = this.data;

    // 保存记住选项的状态
    wx.setStorageSync('rememberUsername', rememberUsername);
    wx.setStorageSync('rememberPassword', rememberPassword);

    // 根据用户选择保存账号
    if (rememberUsername) {
      wx.setStorageSync('savedUsername', username);
    } else {
      wx.removeStorageSync('savedUsername');
    }

    // 根据用户选择保存密码
    if (rememberPassword) {
      wx.setStorageSync('savedPassword', password);
    } else {
      wx.removeStorageSync('savedPassword');
    }
  },

  // 页面分享
  onShareAppMessage: function() {
    return {
      title: '一站津贴查询',
      path: '/pages/stationJintie/login'
    };
  }
});