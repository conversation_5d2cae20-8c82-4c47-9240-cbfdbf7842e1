<!--一站津贴查询登录页面-->
<view class="container">
  <view class="login-card">
    <view class="header">
      <view class="title">一站津贴查询</view>
      <view class="subtitle">请输入用户名和密码</view>
    </view>

    <view class="form">
      <view class="input-group">
        <view class="input-label">
          <text class="icon">👤</text>
          <text>用户名</text>
        </view>
        <input
          class="input-field"
          type="text"
          placeholder="请输入用户名"
          value="{{username}}"
          bindinput="onUsernameInput"
          maxlength="20"
        />
      </view>

      <view class="input-group">
        <view class="input-label">
          <text class="icon">🔒</text>
          <text>密码</text>
        </view>
        <input
          class="input-field"
          type="text"
          password="{{!showPassword}}"
          placeholder="请输入密码"
          value="{{password}}"
          bindinput="onPasswordInput"
          maxlength="20"
        />
        <view class="password-toggle" bindtap="togglePassword">
          <text class="toggle-icon">{{showPassword ? '🙈' : '👁️'}}</text>
        </view>
      </view>

      <!-- 记住功能选项 -->
      <view class="remember-options">
        <view class="remember-item" bindtap="toggleRememberUsername">
          <checkbox
            class="remember-checkbox"
            value="username"
            checked="{{rememberUsername}}"
          />
          <text class="remember-text">记住账号</text>
        </view>
        <view class="remember-item" bindtap="toggleRememberPassword">
          <checkbox
            class="remember-checkbox"
            value="password"
            checked="{{rememberPassword}}"
          />
          <text class="remember-text">记住密码</text>
        </view>
      </view>

      <button
        class="login-btn {{isLoading ? 'loading' : ''}}"
        bindtap="handleLogin"
        disabled="{{isLoading || !username || !password}}"
      >
        <text wx:if="{{!isLoading}}">登录</text>
        <text wx:else>登录中...</text>
      </button>
    </view>

    <view class="tips">
      <view class="tip-item">
        <text class="tip-icon">🔐</text>
        <text class="tip-text">如需账号请联系系统管理员申请</text>
      </view>
      <view class="tip-item">
        <text class="tip-icon">📱</text>
        <text class="tip-text">已登录用户可直接查看津贴信息</text>
      </view>
    </view>
  </view>
</view>