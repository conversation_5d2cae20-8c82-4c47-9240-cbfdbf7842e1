/* 一站津贴查询登录页面样式 */
.container {
  min-height: 100vh;
  background: #667eea;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
}

.login-card {
  width: 100%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.98);
  border-radius: 32rpx;
  padding: 80rpx 50rpx;
  box-shadow:
    0 32rpx 80rpx rgba(0, 0, 0, 0.15),
    0 0 0 1rpx rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20rpx);
  position: relative;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
  position: relative;
}

.title {
  font-size: 52rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 30rpx;
  color: #666;
  opacity: 0.9;
  font-weight: 400;
}



.form {
  margin-bottom: 50rpx;
}

.input-group {
  position: relative;
  margin-bottom: 40rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  font-size: 30rpx;
  color: #555;
  font-weight: 600;
}

.icon {
  margin-right: 16rpx;
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0,0,0,0.1));
}

.input-field {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(145deg, #f8f9fa, #ffffff);
  border: 2rpx solid #e9ecef;
  border-radius: 16rpx;
  padding: 0 28rpx;
  font-size: 32rpx;
  color: #333;
  box-sizing: border-box;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  box-shadow:
    inset 0 2rpx 4rpx rgba(0,0,0,0.06),
    0 1rpx 3rpx rgba(0,0,0,0.1);
}

.input-field:focus {
  border-color: #667eea;
  background: #fff;
  box-shadow:
    0 0 0 8rpx rgba(102, 126, 234, 0.12),
    inset 0 2rpx 4rpx rgba(0,0,0,0.06),
    0 8rpx 25rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.password-toggle {
  position: absolute;
  right: 28rpx;
  bottom: 24rpx;
  width: 48rpx;
  height: 48rpx;
  padding: 0;
  z-index: 10;
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:active {
  background: rgba(102, 126, 234, 0.1);
  transform: scale(0.95);
}

.toggle-icon {
  font-size: 36rpx;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

/* 记住功能样式 */
.remember-options {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 30rpx 0 20rpx 0;
  padding: 0 20rpx;
}

.remember-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.remember-checkbox {
  margin-right: 12rpx;
  transform: scale(0.9);
}

.remember-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  user-select: none;
}

.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 34rpx;
  font-weight: 700;
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow:
    0 12rpx 32rpx rgba(102, 126, 234, 0.4),
    0 2rpx 8rpx rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.login-btn:not([disabled]):active {
  transform: translateY(4rpx) scale(0.98);
  box-shadow:
    0 6rpx 16rpx rgba(102, 126, 234, 0.4),
    0 1rpx 4rpx rgba(0,0,0,0.1);
}

.login-btn:not([disabled]):active::before {
  left: 100%;
}

.login-btn[disabled] {
  background: linear-gradient(135deg, #bbb 0%, #999 100%);
  box-shadow:
    0 4rpx 12rpx rgba(0,0,0,0.1),
    0 1rpx 3rpx rgba(0,0,0,0.1);
  opacity: 0.7;
  cursor: not-allowed;
}

.login-btn.loading {
  background: linear-gradient(135deg, #999 0%, #777 100%);
  animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.tips {
  margin-top: 50rpx;
  padding-top: 40rpx;
  border-top: 1rpx solid rgba(0,0,0,0.08);
}

.tip-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  padding: 16rpx;
  background: rgba(102, 126, 234, 0.04);
  border-radius: 12rpx;
  border-left: 4rpx solid rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.tip-item:hover {
  background: rgba(102, 126, 234, 0.08);
  transform: translateX(4rpx);
}

.tip-icon {
  margin-right: 16rpx;
  font-size: 32rpx;
  flex-shrink: 0;
  filter: drop-shadow(0 1rpx 2rpx rgba(0,0,0,0.1));
}

.tip-text {
  flex: 1;
  font-weight: 500;
}



/* 响应式设计 */
@media (max-width: 750rpx) {
  .container {
    padding: 30rpx 15rpx;
  }

  .login-card {
    margin: 0;
    padding: 60rpx 35rpx;
    border-radius: 24rpx;
  }

  .title {
    font-size: 46rpx;
  }

  .subtitle {
    font-size: 28rpx;
  }

  .input-field, .login-btn {
    height: 88rpx;
    font-size: 30rpx;
  }

  .input-label {
    font-size: 28rpx;
  }

  .icon {
    font-size: 32rpx;
  }

  .tip-item {
    font-size: 24rpx;
    padding: 12rpx;
  }

  .remember-options {
    justify-content: space-between;
    padding: 0 30rpx;
  }

  .remember-item {
    flex: 1;
    justify-content: flex-start;
  }

  .remember-text {
    font-size: 26rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .login-card {
    background: rgba(30, 30, 30, 0.95);
    color: #fff;
  }

  .title {
    background: linear-gradient(135deg, #8b9dc3 0%, #9575cd 100%);
    background-clip: text;
    -webkit-background-clip: text;
  }

  .subtitle {
    color: #ccc;
  }

  .input-label {
    color: #ddd;
  }

  .input-field {
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-color: #444;
    color: #fff;
  }

  .input-field:focus {
    border-color: #8b9dc3;
    background: #333;
  }

  .tip-item {
    background: rgba(139, 157, 195, 0.1);
    color: #ccc;
    border-left-color: rgba(139, 157, 195, 0.3);
  }

  .remember-text {
    color: #ccc;
  }
}