<!--证件照换背景页面-->
<view class="container">


  <!-- 主要内容 -->
  <view class="main-content">
    <!-- 上传区域 -->
    <view class="upload-section">
      <view class="upload-area" bindtap="chooseImage" wx:if="{{!selectedImage}}">
        <view class="upload-icon">📷</view>
        <view class="upload-text">点击上传证件照</view>
        <view class="upload-hint">支持 JPG、PNG 格式，建议尺寸 295×413</view>
      </view>
      
      <!-- 图片预览 -->
      <view class="image-preview-container" wx:if="{{selectedImage}}">
        <view class="image-preview">
          <image src="{{selectedImage}}" mode="widthFix" class="preview-image" bindtap="previewSelectedImage"></image>
        </view>
        <view class="image-actions">
          <view class="action-row">
            <button class="btn btn-secondary" bindtap="chooseImage">重新选择</button>
            <view class="smart-optimization-toggle">
              <text class="toggle-text">智能优化</text>
              <switch checked="{{enableSmartOptimization}}" bindchange="toggleSmartOptimization" color="#007AFF"/>
            </view>
          </view>
          <view class="toggle-hint">自动检测并优化非标准证件照</view>
        </view>
      </view>

      <!-- 功能选择 -->
      <view class="function-selection" wx:if="{{selectedImage && !processing && !resultImage}}">
        <view class="section-title">选择功能</view>
        <view class="function-options">
          <view class="function-card" bindtap="selectFunction" data-function="compress">
            <view class="function-icon">📦</view>
            <view class="function-name">压缩图片</view>
            <view class="function-desc">调整文件大小，减小存储空间</view>
          </view>
          <view class="function-card" bindtap="selectFunction" data-function="background">
            <view class="function-icon">🎨</view>
            <view class="function-name">换背景</view>
            <view class="function-desc">更换证件照背景颜色</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 背景色选择 -->
    <view class="color-selection" wx:if="{{selectedFunction === 'background'}}">
      <view class="section-title">选择背景色</view>
      <view class="color-grid">
        <view class="color-item {{selectedColor === 'red' ? 'selected' : ''}}" bindtap="selectColor" data-color="red">
          <view class="color-option red {{selectedColor === 'red' ? 'selected' : ''}}"></view>
          <view class="color-label">红色</view>
        </view>
        <view class="color-item {{selectedColor === 'blue' ? 'selected' : ''}}" bindtap="selectColor" data-color="blue">
          <view class="color-option blue {{selectedColor === 'blue' ? 'selected' : ''}}"></view>
          <view class="color-label">蓝色</view>
        </view>
        <view class="color-item {{selectedColor === 'white' ? 'selected' : ''}}" bindtap="selectColor" data-color="white">
          <view class="color-option white {{selectedColor === 'white' ? 'selected' : ''}}"></view>
          <view class="color-label">白色</view>
        </view>
        <view class="color-item {{selectedColor === 'blueWhite' ? 'selected' : ''}}" bindtap="selectColor" data-color="blueWhite">
          <view class="color-option blue-white {{selectedColor === 'blueWhite' ? 'selected' : ''}}"></view>
          <view class="color-label">蓝白渐变</view>
        </view>
      </view>
    </view>

    <!-- 尺寸选择 -->
    <view class="size-selection" wx:if="{{selectedFunction === 'background'}}">
      <view class="section-title">选择照片尺寸</view>
      <view class="size-grid">
        <view class="size-option {{selectedSize === '1inch' ? 'selected' : ''}}" bindtap="selectSize" data-size="1inch">
          <view class="size-preview">
            <view class="size-rect" style="width: 20px; height: 28px;"></view>
          </view>
          <view class="size-info">
            <view class="size-name">一寸照</view>
            <view class="size-detail">25×35mm</view>
          </view>
        </view>
        <view class="size-option {{selectedSize === '2inch' ? 'selected' : ''}}" bindtap="selectSize" data-size="2inch">
          <view class="size-preview">
            <view class="size-rect" style="width: 24px; height: 34px;"></view>
          </view>
          <view class="size-info">
            <view class="size-name">二寸照</view>
            <view class="size-detail">35×49mm</view>
          </view>
        </view>
        <view class="size-option {{selectedSize === 'small2inch' ? 'selected' : ''}}" bindtap="selectSize" data-size="small2inch">
          <view class="size-preview">
            <view class="size-rect" style="width: 24px; height: 31px;"></view>
          </view>
          <view class="size-info">
            <view class="size-name">小二寸</view>
            <view class="size-detail">35×45mm</view>
          </view>
        </view>
        <view class="size-option {{selectedSize === 'passport' ? 'selected' : ''}}" bindtap="selectSize" data-size="passport">
          <view class="size-preview">
            <view class="size-rect" style="width: 26px; height: 32px;"></view>
          </view>
          <view class="size-info">
            <view class="size-name">护照照片</view>
            <view class="size-detail">33×48mm</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 压缩选项 -->
    <view class="compress-selection" wx:if="{{selectedFunction === 'compress'}}">
      <view class="section-title">选择压缩大小</view>
      <view class="compress-options">
        <view class="compress-option {{targetFileSize === 50 ? 'selected' : ''}}" bindtap="selectFileSize" data-size="50">
          <view class="compress-value">50KB</view>
          <view class="compress-desc">标准压缩</view>
        </view>
        <view class="compress-option {{targetFileSize === 100 ? 'selected' : ''}}" bindtap="selectFileSize" data-size="100">
          <view class="compress-value">100KB</view>
          <view class="compress-desc">推荐大小</view>
        </view>
        <view class="compress-option {{targetFileSize === 200 ? 'selected' : ''}}" bindtap="selectFileSize" data-size="200">
          <view class="compress-value">200KB</view>
          <view class="compress-desc">高质量</view>
        </view>
        <view class="compress-option {{isCustomSize ? 'selected' : ''}}" bindtap="selectCustomSize">
          <view class="compress-value">自定义</view>
          <view class="compress-desc">{{customSize ? customSize + 'KB' : '输入大小'}}</view>
        </view>
      </view>

      <!-- 自定义大小输入 -->
      <view class="custom-size-input" wx:if="{{isCustomSize}}">
        <view class="input-container">
          <input type="number"
                 placeholder="请输入大小"
                 value="{{customSize}}"
                 bindinput="onCustomSizeInput"
                 class="size-input" />
          <text class="input-unit">KB</text>
        </view>
        <view class="size-range-hint">范围：10KB - 2048KB</view>
      </view>
    </view>

    <!-- 进度条 -->
    <view class="progress-section" wx:if="{{processing}}">
      <view class="progress-bar">
        <view class="progress-fill" style="width: {{progress}}%;"></view>
      </view>
      <view class="status-text">{{statusText}}</view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-buttons" wx:if="{{selectedFunction && !processing}}">
      <!-- 换背景按钮 -->
      <button class="btn btn-primary {{!selectedColor || !selectedSize ? 'disabled' : ''}}"
              bindtap="startProcess"
              disabled="{{!selectedColor || !selectedSize}}"
              wx:if="{{selectedFunction === 'background'}}">
        开始换背景
      </button>

      <!-- 压缩按钮 -->
      <button class="btn btn-primary {{!targetFileSize ? 'disabled' : ''}}"
              bindtap="startCompress"
              disabled="{{!targetFileSize}}"
              wx:if="{{selectedFunction === 'compress'}}">
        开始压缩
      </button>
    </view>

    <!-- 结果展示 -->
    <view class="result-section" wx:if="{{resultImage}}">
      <view class="section-title">处理结果</view>

      <view class="image-preview-container">
        <view class="image-preview">
          <image src="{{resultImage}}" mode="widthFix" class="preview-image" bindtap="previewResult"></image>
        </view>
      </view>
      <view class="result-actions">
        <button class="btn btn-secondary" bindtap="resetAll">处理新照片</button>
        <button class="btn btn-primary" bindtap="saveImage">保存图片</button>
      </view>

      <!-- 隐私保护提示 -->
      <view class="privacy-notice">
        <view class="notice-icon">🔒</view>
        <view class="notice-content">
          <view class="notice-title">隐私保护提示</view>
          <view class="notice-text">为了保护您的隐私，本APP不提供照片储存功能，返回后自动删除，请点击保存按钮进行保存！如果不能保存到相册，请点击小程序的右上角的三个点——选择设置——开启相册权限</view>
        </view>
      </view>
    </view>

    <!-- 功能说明 -->
    <view class="features-section" wx:if="{{!selectedImage}}">
      <view class="section-title">功能特点</view>
      <view class="feature-list">
        <view class="feature-item">
          <view class="feature-icon">✓</view>
          <text>AI智能抠图，精准识别人像边缘</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">✓</view>
          <text>支持红、蓝、白、蓝白渐变等证件照背景</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">✓</view>
          <text>多种尺寸：一寸、二寸、小二寸、护照照片</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">✓</view>
          <text>高清输出，保持原图质量</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">✓</view>
          <text>智能压缩，自定义文件大小，节省存储空间</text>
        </view>
        <view class="feature-item">
          <view class="feature-icon">✓</view>
          <text>完全免费，无广告，无水印，纯净体验</text>
        </view>
      </view>
    </view>
  </view>

  <!-- Canvas用于图像处理 -->
  <canvas type="2d" id="processCanvas" class="process-canvas"></canvas>
</view>
