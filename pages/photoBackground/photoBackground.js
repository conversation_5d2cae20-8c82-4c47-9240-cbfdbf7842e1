// 证件照换背景页面

// 错误上报函数
function reportError(errorType, errorMessage, errorDetails = {}) {
  try {
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/error_report.php',
      method: 'POST',
      data: {
        module: 'photoBackground',
        errorType: errorType,
        errorMessage: errorMessage,
        errorDetails: errorDetails,
        timestamp: new Date().toISOString(),
        userAgent: wx.getSystemInfoSync()
      },
      fail: (err) => {
        // 错误上报失败时不影响用户体验，只在控制台记录
        console.warn('错误上报失败:', err);
      }
    });
  } catch (e) {
    console.warn('错误上报异常:', e);
  }
}

Page({
  data: {
    // 选中的图片（显示用）
    selectedImage: '',
    // 原始图片路径（处理用）
    originalImagePath: '',
    // 选中的背景色
    selectedColor: '',
    // 选中的尺寸
    selectedSize: '',
    // 选中的功能 ('compress' | 'background')
    selectedFunction: '',
    // 目标文件大小（KB）
    targetFileSize: 100,
    // 是否自定义大小
    isCustomSize: false,
    // 自定义大小值
    customSize: '',
    // 处理结果
    resultImage: '',
    // 处理状态
    processing: false,
    // 进度
    progress: 0,
    // 状态文本
    statusText: '准备处理...',
    // 是否启用智能优化（默认启用）
    enableSmartOptimization: true
  },

  onLoad() {
    // 页面加载完成
  },

  /**
   * 选择图片
   */
  chooseImage() {
    // 使用 chooseImage API，可以更好地控制默认选项
    wx.chooseImage({
      count: 1,
      sourceType: ['album', 'camera'],
      sizeType: ['original', 'compressed'], // 原图默认选中，用户可以取消选择压缩图
      success: async (res) => {
        const imagePath = res.tempFilePaths[0]

        // 显示处理提示
        wx.showLoading({
          title: '正在分析照片...',
          mask: true
        })

        try {
          // 预处理图片，提高质量
          const processedPath = await this.preprocessImage(imagePath)

          let finalPath = processedPath

          // 只有在启用智能优化时才进行分析和优化
          if (this.data.enableSmartOptimization) {
            // 先进行快速本地分析，判断是否需要智能优化
            const needsOptimization = await this.quickPhotoAnalysis(processedPath)

            if (needsOptimization) {
              // 只有在需要时才调用智能优化API
              try {
                finalPath = await this.smartOptimizePhoto(processedPath)
              } catch (optimizeError) {
                console.warn('智能优化失败，使用原图:', optimizeError)
                finalPath = processedPath
              }
            }
          }

          this.setData({
            selectedImage: finalPath,
            originalImagePath: finalPath,
            resultImage: '', // 清空之前的结果
            selectedColor: this.data.selectedFunction === 'background' ? (this.data.selectedColor || 'red') : this.data.selectedColor,
            selectedSize: this.data.selectedFunction === 'background' ? (this.data.selectedSize || '1inch') : this.data.selectedSize,
            processing: false,
            progress: 0,
            statusText: '准备处理...'
          })

        } catch (error) {
          console.warn('图片处理失败，使用原图:', error)

          try {
            // 如果处理失败，使用原图
            const processedPath = await this.preprocessImage(imagePath)
            this.setData({
              selectedImage: processedPath,
              originalImagePath: processedPath,
              resultImage: '',
              selectedColor: this.data.selectedFunction === 'background' ? (this.data.selectedColor || 'red') : this.data.selectedColor,
              selectedSize: this.data.selectedFunction === 'background' ? (this.data.selectedSize || '1inch') : this.data.selectedSize,
              processing: false,
              progress: 0,
              statusText: '准备处理...'
            })
          } catch (preprocessError) {
            console.error('预处理也失败了:', preprocessError)
          }
        } finally {
          // 确保只有一个hideLoading与showLoading配对
          wx.hideLoading()
        }
      },
      fail: (error) => {
        // 上报图片选择失败错误
        reportError('IMAGE_SELECT_ERROR', '选择图片失败', {
          originalError: error.errMsg || error.message
        });
        wx.showToast({
          title: '选择图片失败',
          icon: 'error'
        })
      }
    })
  },



  /**
   * 选择背景色
   */
  selectColor(e) {
    const color = e.currentTarget.dataset.color
    this.setData({
      selectedColor: color
    })
  },

  /**
   * 选择尺寸
   */
  selectSize(e) {
    const size = e.currentTarget.dataset.size
    this.setData({
      selectedSize: size
    })
  },

  /**
   * 选择功能
   */
  selectFunction(e) {
    const func = e.currentTarget.dataset.function
    this.setData({
      selectedFunction: func,
      // 设置默认值
      selectedColor: func === 'background' ? 'red' : this.data.selectedColor,
      selectedSize: func === 'background' ? '1inch' : this.data.selectedSize,
      targetFileSize: func === 'compress' ? 100 : this.data.targetFileSize
    })

  },

  /**
   * 切换智能优化开关
   */
  toggleSmartOptimization(e) {
    const newValue = e.detail.value
    this.setData({
      enableSmartOptimization: newValue
    })

    wx.showToast({
      title: newValue ? '已启用智能优化' : '已关闭智能优化',
      icon: 'success',
      duration: 1500
    })
  },

  /**
   * 选择文件大小
   */
  selectFileSize(e) {
    const size = parseInt(e.currentTarget.dataset.size)
    this.setData({
      targetFileSize: size,
      isCustomSize: false,
      customSize: ''
    })

  },

  /**
   * 选择自定义大小
   */
  selectCustomSize() {
    this.setData({
      isCustomSize: true,
      targetFileSize: 0
    })
  },

  /**
   * 自定义大小输入
   */
  onCustomSizeInput(e) {
    const value = e.detail.value
    const size = parseInt(value) || 0

    // 验证范围
    if (size > 0 && size <= 2048) {
      this.setData({
        customSize: value,
        targetFileSize: size
      })
    } else {
      this.setData({
        customSize: value,
        targetFileSize: 0
      })
    }
  },

  /**
   * 开始压缩
   */
  async startCompress() {
    if (!this.data.originalImagePath) {
      wx.showToast({
        title: '请先选择图片',
        icon: 'none'
      })
      return
    }

    if (!this.data.targetFileSize || this.data.targetFileSize < 10 || this.data.targetFileSize > 2048) {
      wx.showToast({
        title: '请选择有效的压缩大小(10KB-2048KB)',
        icon: 'none'
      })
      return
    }

    this.setData({
      processing: true,
      progress: 0,
      statusText: '正在压缩图片...'
    })

    try {
      // 读取图片文件
      const imageData = await this.getImageBase64(this.data.originalImagePath)

      this.updateProgress(30, '准备压缩...')

      // 调用压缩API
      const response = await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://sunxiyue.com/zdh/api/image_compress.php',
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: {
            imageData: imageData,
            targetSize: this.data.targetFileSize,
            format: 'jpg'
          },
          success: resolve,
          fail: reject
        })
      })

      this.updateProgress(80, '压缩完成...')

      if (response.data && response.data.success) {
        this.setData({
          resultImage: response.data.data.compressedImage,
          processing: false,
          progress: 100,
          statusText: `压缩完成！原始大小: ${Math.round(response.data.data.originalSize/1024)}KB，压缩后: ${Math.round(response.data.data.compressedSize/1024)}KB`
        })

        wx.showToast({
          title: '压缩成功',
          icon: 'success'
        })
      } else {
        throw new Error(response.data?.error || '压缩失败，请检查网络连接')
      }

    } catch (error) {
      // 上报图片压缩错误
      reportError('IMAGE_COMPRESS_ERROR', '图片压缩失败', {
        originalError: error.message,
        targetFileSize: this.data.targetFileSize
      });

      this.setData({
        processing: false,
        progress: 0
      })

      wx.showToast({
        title: error.message || '压缩失败，请重试',
        icon: 'error'
      })
    }
  },

  /**
   * 开始处理背景
   */
  async startProcess() {
    if (!this.data.originalImagePath) {
      wx.showToast({
        title: '请先选择图片',
        icon: 'none'
      })
      return
    }

    if (!this.data.selectedColor || !this.data.selectedSize) {
      wx.showToast({
        title: '请选择背景色和尺寸',
        icon: 'none'
      })
      return
    }

    this.setData({
      processing: true,
      progress: 0,
      statusText: '正在上传图片...'
    })

    // 显示高精度处理提示
    wx.showLoading({
      title: 'AI高精度处理中...',
      mask: true
    })

    try {
      // 将图片转换为base64
      this.updateProgress(10, '正在处理图片...')
      const imageBase64 = await this.imageToBase64(this.data.originalImagePath)

      this.updateProgress(30, '正在进行人像分割...')

      // 调用API进行人像分割
      const result = await this.callPortraitAPI(imageBase64)

      this.updateProgress(70, '正在高精度合成背景...')

      // 处理背景替换（高精度）
      const resultPath = await this.processBackgroundHighQuality(result, this.data.selectedColor, this.data.selectedSize)

      this.updateProgress(90, '正在优化图片质量...')

      // 短暂延迟，让用户看到处理完成
      await new Promise(resolve => setTimeout(resolve, 500))

      this.updateProgress(100, '高质量处理完成！')

      this.setData({
        resultImage: resultPath,
        processing: false
      })

      wx.hideLoading()
      wx.showToast({
        title: '高质量处理完成！',
        icon: 'success',
        duration: 2000
      })

    } catch (error) {
      // 上报背景处理错误
      reportError('BACKGROUND_PROCESS_ERROR', '背景处理失败', {
        originalError: error.message,
        selectedColor: this.data.selectedColor,
        selectedSize: this.data.selectedSize
      });

      this.setData({
        processing: false,
        statusText: '处理失败'
      })

      wx.hideLoading()
      wx.showModal({
        title: '处理失败',
        content: error.message || '图片处理失败，请重试或更换图片',
        showCancel: false
      })
    }
  },

  /**
   * 更新进度
   */
  updateProgress(progress, statusText) {
    this.setData({
      progress,
      statusText: statusText || this.data.statusText
    })
  },

  /**
   * 获取图片的Base64数据
   */
  async getImageBase64(imagePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().readFile({
        filePath: imagePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data)
        },
        fail: reject
      })
    })
  },

  /**
   * 将base64数据转换为临时文件
   */
  async base64ToTempFile(base64Data) {
    return new Promise((resolve, reject) => {
      // 提取base64数据部分
      const base64 = base64Data.split(',')[1]

      // 生成临时文件路径
      const tempFilePath = wx.env.USER_DATA_PATH + '/temp_' + Date.now() + '.jpg'

      // 写入临时文件
      wx.getFileSystemManager().writeFile({
        filePath: tempFilePath,
        data: base64,
        encoding: 'base64',
        success: () => {
          resolve(tempFilePath)
        },
        fail: reject
      })
    })
  },

  /**
   * 快速本地分析照片是否需要优化
   */
  async quickPhotoAnalysis(imagePath) {
    try {
      // 获取图片信息
      const imageInfo = await new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        })
      })

      const { width, height } = imageInfo
      const aspectRatio = width / height

      // 检查是否是标准证件照比例
      const standardRatios = [
        { name: '1inch', ratio: 295/413 },
        { name: '2inch', ratio: 413/579 },
        { name: 'passport', ratio: 390/567 }
      ]

      let isStandardRatio = false
      for (const standard of standardRatios) {
        if (Math.abs(aspectRatio - standard.ratio) < 0.1) {
          isStandardRatio = true
          break
        }
      }

      // 判断是否需要优化的条件
      const needsOptimization =
        !isStandardRatio ||                    // 不是标准比例
        width < 300 || height < 300 ||         // 尺寸太小
        aspectRatio > 1.2 ||                   // 太宽（可能是横向照片）
        (width > height && aspectRatio > 1.0)  // 横向照片

      return needsOptimization

    } catch (error) {
      console.warn('快速分析失败:', error)
      // 分析失败时，为了安全起见，不进行优化
      return false
    }
  },

  /**
   * 智能优化照片
   */
  async smartOptimizePhoto(imagePath) {
    try {
      // 将图片转换为base64
      const imageBase64 = await this.imageToBase64(imagePath)

      // 获取目标尺寸
      const targetSize = this.data.selectedSize || '1inch'

      // 调用智能优化API
      const result = await new Promise((resolve, reject) => {
        wx.request({
          url: 'https://sunxiyue.com/zdh/api/smart_photo_optimizer.php',
          method: 'POST',
          header: {
            'content-type': 'application/json'
          },
          data: {
            image: imageBase64,
            targetSize: targetSize
          },
          success: (res) => {
            if (res.statusCode === 200) {
              if (res.data.code === 0) {
                resolve(res.data.data)
              } else {
                reject(new Error(res.data.message || '智能优化失败'))
              }
            } else {
              reject(new Error(`HTTP ${res.statusCode}`))
            }
          },
          fail: (err) => {
            reject(new Error(err.errMsg || '网络请求失败'))
          }
        })
      })

      // 如果需要优化，使用优化后的图片
      if (result.needsOptimization && result.optimizedImage) {
        // 将优化后的base64转换为临时文件
        const optimizedPath = await this.base64ToTempFile(`data:image/jpeg;base64,${result.optimizedImage}`)

        // 显示优化提示
        const reasons = result.analysis.reasons || []
        if (reasons.length > 0) {
          // 根据优化原因显示不同的提示
          let toastTitle = '智能优化完成'
          if (reasons[0].includes('已是标准证件照')) {
            toastTitle = '已是标准证件照'
          } else if (reasons[0].includes('人脸')) {
            toastTitle = '已优化人脸位置'
          } else if (reasons[0].includes('比例')) {
            toastTitle = '已优化照片比例'
          } else if (reasons[0].includes('大小')) {
            toastTitle = '已优化照片尺寸'
          }

          wx.showToast({
            title: toastTitle,
            icon: 'success',
            duration: 2000
          })
        }

        return optimizedPath
      } else {
        // 不需要优化，返回原图
        return imagePath
      }

    } catch (error) {
      console.warn('智能优化失败:', error)
      throw error
    }
  },

  /**
   * 图片预处理，提高质量
   */
  async preprocessImage(imagePath) {
    try {
      // 获取图片信息
      const imageInfo = await new Promise((resolve, reject) => {
        wx.getImageInfo({
          src: imagePath,
          success: resolve,
          fail: reject
        })
      })

      // 如果图片尺寸过大，进行适当压缩但保持高质量
      if (imageInfo.width > 2000 || imageInfo.height > 2000) {
        const canvas = wx.createOffscreenCanvas({ type: '2d' })
        const ctx = canvas.getContext('2d')

        // 计算合适的尺寸（保持宽高比）
        const maxSize = 2000
        let { width, height } = imageInfo
        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width
            width = maxSize
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height
            height = maxSize
          }
        }

        canvas.width = width
        canvas.height = height

        const img = canvas.createImage()
        await new Promise((resolve, reject) => {
          img.onload = resolve
          img.onerror = reject
          img.src = imagePath
        })

        // 使用高质量绘制
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'
        ctx.drawImage(img, 0, 0, width, height)

        // 导出高质量图片
        return await new Promise((resolve, reject) => {
          wx.canvasToTempFilePath({
            canvas: canvas,
            fileType: 'jpg', // 使用JPG格式
            quality: 0.95,   // 高质量
            success: (res) => resolve(res.tempFilePath),
            fail: reject
          })
        })
      }

      return imagePath // 如果不需要处理，返回原路径
    } catch (error) {
      return imagePath
    }
  },

  /**
   * 图片转base64
   */
  imageToBase64(imagePath) {
    return new Promise((resolve, reject) => {
      wx.getFileSystemManager().readFile({
        filePath: imagePath,
        encoding: 'base64',
        success: (res) => {
          resolve(res.data)
        },
        fail: reject
      })
    })
  },

  /**
   * 调用人像分割API
   */
  callPortraitAPI(imageBase64) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://sunxiyue.com/zdh/api/portrait_segmentation.php',
        method: 'POST',
        header: {
          'content-type': 'application/json'
        },
        data: {
          image: imageBase64
        },
        success: (res) => {
          if (res.statusCode === 200) {
            if (res.data.code === 0) {
              resolve(res.data.data)
            } else {
              // 上报API调用失败错误
              reportError('API_CALL_ERROR', 'Portrait Segmentation API调用失败', {
                apiResponse: res.data,
                statusCode: res.statusCode
              });
              reject(new Error(res.data.message || 'API调用失败'))
            }
          } else {
            // 上报HTTP错误
            reportError('HTTP_ERROR', `Portrait Segmentation API HTTP错误: ${res.statusCode}`, {
              statusCode: res.statusCode,
              url: 'https://sunxiyue.com/zdh/api/portrait_segmentation.php'
            });
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (err) => {
          // 上报网络请求失败错误
          reportError('NETWORK_ERROR', 'Portrait Segmentation API网络请求失败', {
            originalError: err.errMsg || err.message,
            url: 'https://sunxiyue.com/zdh/api/portrait_segmentation.php'
          });
          reject(new Error(err.errMsg || '网络请求失败'))
        }
      })
    })
  },

  /**
   * 高质量背景替换处理
   */
  async processBackgroundHighQuality(apiResult, colorType, sizeType) {
    try {
      // 背景色配置
      const backgroundColors = {
        red: '#DC2626',
        blue: '#2563EB',
        white: '#FFFFFF',
        blueWhite: {
          type: 'gradient',
          colors: ['#2563eb', '#3b82f6', '#93c5fd', '#ffffff']
        }
      }

      // 尺寸配置（使用更高分辨率）
      const photoSizes = {
        '1inch': { width: 590, height: 826 },    // 2倍分辨率
        '2inch': { width: 826, height: 1158 },   // 2倍分辨率
        'small2inch': { width: 826, height: 1062 }, // 2倍分辨率
        'passport': { width: 780, height: 1134 }  // 2倍分辨率
      }

      const backgroundColor = backgroundColors[colorType]
      const outputSize = photoSizes[sizeType]

      // 使用高精度Canvas进行背景替换
      const highResResult = await this.replaceBackgroundWithCanvas(
        apiResult.resultImage,
        backgroundColor,
        outputSize
      )

      // 缩放到目标尺寸（抗锯齿处理）
      return await this.resizeImageWithAntialiasing(highResResult, {
        width: Math.floor(outputSize.width / 2),
        height: Math.floor(outputSize.height / 2)
      })

    } catch (error) {
      throw new Error('背景处理失败: ' + error.message)
    }
  },

  /**
   * 带抗锯齿的图片缩放
   */
  async resizeImageWithAntialiasing(imagePath, targetSize) {
    return new Promise((resolve, reject) => {
      const canvas = wx.createOffscreenCanvas({ type: '2d' })
      const ctx = canvas.getContext('2d')

      canvas.width = targetSize.width
      canvas.height = targetSize.height

      // 启用高质量缩放
      ctx.imageSmoothingEnabled = true
      ctx.imageSmoothingQuality = 'high'

      const img = canvas.createImage()
      img.onload = () => {
        ctx.drawImage(img, 0, 0, targetSize.width, targetSize.height)

        wx.canvasToTempFilePath({
          canvas: canvas,
          fileType: 'jpg',  // 使用JPG格式
          quality: 0.95,    // 高质量
          success: (res) => resolve(res.tempFilePath),
          fail: reject
        })
      }
      img.onerror = reject
      img.src = imagePath
    })
  },

  /**
   * 使用Canvas替换背景（高精度版本）
   */
  async replaceBackgroundWithCanvas(imageBase64, backgroundColor, outputSize) {
    return new Promise((resolve, reject) => {
      const query = wx.createSelectorQuery()
      query.select('#processCanvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node
          const ctx = canvas.getContext('2d')

          // 设置高分辨率canvas
          const dpr = wx.getDeviceInfo().pixelRatio || 2
          const scaledWidth = outputSize.width * dpr
          const scaledHeight = outputSize.height * dpr

          canvas.width = scaledWidth
          canvas.height = scaledHeight
          ctx.scale(dpr, dpr)

          // 启用高质量渲染
          ctx.imageSmoothingEnabled = true
          ctx.imageSmoothingQuality = 'high'

          // 绘制背景
          if (backgroundColor.type === 'gradient') {
            const gradient = ctx.createLinearGradient(0, 0, 0, outputSize.height)
            backgroundColor.colors.forEach((color, index) => {
              gradient.addColorStop(index / (backgroundColor.colors.length - 1), color)
            })
            ctx.fillStyle = gradient
          } else {
            ctx.fillStyle = backgroundColor
          }
          ctx.fillRect(0, 0, outputSize.width, outputSize.height)

          // 加载并绘制处理后的人像
          const img = canvas.createImage()
          img.onload = () => {
            // 使用高质量绘制
            ctx.globalCompositeOperation = 'source-over'
            ctx.drawImage(img, 0, 0, outputSize.width, outputSize.height)

            // 导出高质量图片
            wx.canvasToTempFilePath({
              canvas: canvas,
              fileType: 'jpg',  // 使用JPG格式，文件更小
              quality: 0.95,    // 高质量，平衡质量和文件大小
              success: (res) => {
                resolve(res.tempFilePath)
              },
              fail: reject
            })
          }
          img.onerror = reject
          img.src = `data:image/png;base64,${imageBase64}`
        })
    })
  },

  /**
   * 保存图片
   */
  async saveImage() {
    if (!this.data.resultImage) {
      wx.showToast({
        title: '没有可保存的图片',
        icon: 'none'
      })
      return
    }

    try {
      let filePath = this.data.resultImage

      // 如果是base64数据，需要先转换为临时文件
      if (this.data.resultImage.startsWith('data:image/')) {
        filePath = await this.base64ToTempFile(this.data.resultImage)
      }

      wx.saveImageToPhotosAlbum({
        filePath: filePath,
        success: () => {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          })
        },
        fail: (error) => {
          // 上报图片保存失败错误
          reportError('IMAGE_SAVE_ERROR', '保存图片到相册失败', {
            originalError: error.errMsg || error.message,
            isAuthError: error.errMsg && error.errMsg.includes('auth deny')
          });

          if (error.errMsg.includes('auth deny')) {
            wx.showModal({
              title: '需要授权',
              content: '需要授权访问相册才能保存图片',
              success: (res) => {
                if (res.confirm) {
                  wx.openSetting()
                }
              }
            })
          } else {
            wx.showToast({
              title: '保存失败: ' + (error.errMsg || '未知错误'),
              icon: 'none'
            })
          }
        }
      })
    } catch (error) {
      // 上报保存图片过程中的通用错误
      reportError('SAVE_PROCESS_ERROR', '保存图片过程中发生错误', {
        originalError: error.message || error.toString()
      });

      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  },

  /**
   * 重置所有状态
   */
  resetAll() {
    this.setData({
      selectedImage: '',
      originalImagePath: '',
      resultImage: '',
      selectedColor: '',
      selectedSize: '',
      selectedFunction: '',
      targetFileSize: 100,
      isCustomSize: false,
      customSize: '',
      processing: false,
      progress: 0,
      statusText: '准备处理...'
    })
  },

  /**
   * 预览上传的图片
   */
  previewSelectedImage() {
    if (this.data.selectedImage) {
      wx.previewImage({
        urls: [this.data.selectedImage],
        current: this.data.selectedImage
      })
    }
  },

  /**
   * 预览结果
   */
  previewResult() {
    if (this.data.resultImage) {
      wx.previewImage({
        urls: [this.data.resultImage],
        current: this.data.resultImage
      })
    }
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '证件照换背景工具',
      path: '/pages/photoBackground/photoBackground'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '证件照换背景工具',
      query: 'from=timeline'
    };
  }
})
