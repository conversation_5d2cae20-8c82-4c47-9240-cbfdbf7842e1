<view class="container compact-container">
  <view class="form-header">
    <text class="form-title">倒班时间录入</text>
  </view>

  <view wx:if="{{!isLoggedIn}}" class="login-box compact-login">
    <view class="login-content">
      <input class="modern-input compact-input"
             placeholder="用户名"
             value="{{username}}"
             bindinput="inputUsername"
             placeholder-style="color:#95a5a6;"/>
             
      <input class="modern-input compact-input"
             password
             placeholder="密码"
             value="{{password}}"
             bindinput="inputPassword"
             placeholder-style="color:#95a5a6;"/>

      <!-- 记住功能选项 -->
      <view class="remember-options">
        <view class="remember-item" bindtap="toggleRememberUsername">
          <checkbox
            class="remember-checkbox"
            value="username"
            checked="{{rememberUsername}}"
          />
          <text class="remember-text">记住账号</text>
        </view>
        <view class="remember-item" bindtap="toggleRememberPassword">
          <checkbox
            class="remember-checkbox"
            value="password"
            checked="{{rememberPassword}}"
          />
          <text class="remember-text">记住密码</text>
        </view>
      </view>

      <button class="login-btn" bindtap="handleLogin">立即登录</button>
    </view>
  </view>

  <!-- 登录后显示 -->
  <view wx:else class="form-box compact-form">
    <view class="form-content">
      <view class="form-item card-style compact-item">
        <text class="item-label compact-label">选择班站</text>
        <radio-group class="station-group" bindchange="changeStation">
          <label class="station-radio">
            <radio value="一站" checked="{{station === '一站'}}" color="#3498db"/>
            <text>一站</text>
          </label>
          <label class="station-radio">
            <radio value="二站" checked="{{station === '二站'}}" color="#2ecc71"/>
            <text>二站</text>
          </label>
        </radio-group>
      </view>

      <view class="form-item card-style">
        <text class="item-label">开始日期</text>
        <picker mode="date" class="modern-picker" bindchange="changeStartDate">
          <view class="picker-content">
            <text class="picker-text">{{startDate || '请选择日期'}}</text>
            <image src="{{icons.calendar}}" class="picker-icon"></image>
          </view>
        </picker>
      </view>

      <view class="form-item card-style">
        <text class="item-label">结束日期</text>
        <picker mode="date" class="modern-picker" bindchange="changeEndDate">
          <view class="picker-content">
            <text class="picker-text">{{endDate || '请选择日期'}}</text>
            <image src="{{icons.calendar}}" class="picker-icon"></image>
          </view>
        </picker>
      </view>

<view class="days-card compact-days">
  <text class="days-label">总出海天数</text>
  <text class="days-value">{{days}} 天</text>
</view>

      <view class="button-group">
        <button class="submit-btn primary" bindtap="submitForm">提交并返回</button>
        <button class="submit-btn secondary" bindtap="submitAndStay">提交继续</button>
      </view>
    </view>
  </view>
</view>