// pages/seaSchedule/admin.js

Page({
  data: {
    isLoggedIn: false,
    username: '',
    password: '',
    station: '一站',
    stationIndex: 0,
    startDate: '',
    endDate: '',
    days: 0,
    editMode: false,
    editId: null,
    remark: '',
    rememberUsername: false,
    rememberPassword: false
  },

  onLoad(options) {
    // 加载记住的账号和密码
    this.loadRememberedCredentials();

    if (options.editData) {
      const editData = JSON.parse(options.editData);
      this.setData({
        station: editData.station,
        stationIndex: editData.station === '一站' ? 0 : 1,
        startDate: editData.start_date,
        endDate: editData.end_date,
        editMode: true,
        editId: editData.id
      });
      this.calculateDays();
    }
  },

  // 输入处理
  inputUsername(e) {
    this.setData({ username: e.detail.value });
  },

  inputPassword(e) {
    this.setData({ password: e.detail.value });
  },

  inputRemark(e) {
    this.setData({ remark: e.detail.value });
  },

  onShow() {
    const token = wx.getStorageSync('token');
    this.setData({ isLoggedIn: !!token });
    // 如果未登录，重新加载记住的凭据
    if (!token) {
      this.loadRememberedCredentials();
    }
  },

  // 加载记住的账号和密码
  loadRememberedCredentials: function() {
    const rememberUsername = wx.getStorageSync('seaScheduleRememberUsername') || false;
    const rememberPassword = wx.getStorageSync('seaScheduleRememberPassword') || false;
    const savedUsername = wx.getStorageSync('seaScheduleSavedUsername') || '';
    const savedPassword = wx.getStorageSync('seaScheduleSavedPassword') || '';

    this.setData({
      rememberUsername: rememberUsername,
      rememberPassword: rememberPassword,
      username: rememberUsername ? savedUsername : '',
      password: rememberPassword ? savedPassword : ''
    });
  },

  // 切换记住账号
  toggleRememberUsername: function() {
    const newValue = !this.data.rememberUsername;
    this.setData({
      rememberUsername: newValue
    });

    // 如果取消记住账号，也要取消记住密码，并清除保存的数据
    if (!newValue) {
      this.setData({
        rememberPassword: false
      });
      wx.removeStorageSync('seaScheduleSavedUsername');
      wx.removeStorageSync('seaScheduleSavedPassword');
    }
  },

  // 切换记住密码
  toggleRememberPassword: function() {
    const newValue = !this.data.rememberPassword;

    // 如果勾选记住密码，自动勾选记住账号
    if (newValue) {
      this.setData({
        rememberUsername: true,
        rememberPassword: true
      });
    } else {
      this.setData({
        rememberPassword: false
      });
      // 如果取消记住密码，清除保存的密码
      wx.removeStorageSync('seaScheduleSavedPassword');
    }
  },

  // 保存账号和密码（如果用户选择了记住）
  saveCredentialsIfNeeded: function() {
    const { username, password, rememberUsername, rememberPassword } = this.data;

    // 保存记住选项的状态
    wx.setStorageSync('seaScheduleRememberUsername', rememberUsername);
    wx.setStorageSync('seaScheduleRememberPassword', rememberPassword);

    // 根据用户选择保存账号
    if (rememberUsername) {
      wx.setStorageSync('seaScheduleSavedUsername', username);
    } else {
      wx.removeStorageSync('seaScheduleSavedUsername');
    }

    // 根据用户选择保存密码
    if (rememberPassword) {
      wx.setStorageSync('seaScheduleSavedPassword', password);
    } else {
      wx.removeStorageSync('seaScheduleSavedPassword');
    }
  },

  // 登录
  handleLogin() {
    if (!this.data.username || !this.data.password) {
      wx.showToast({ title: '请输入完整信息', icon: 'none' });
      return;
    }
  
    wx.showLoading({ title: '登录中...' });
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      method: 'POST',
      data: {
        action: 'login',
        username: this.data.username,
        password: this.data.password
      },
      complete: () => wx.hideLoading(), // 统一在这里关闭
      success: (res) => {
        if (res.data.status === 'success') {
          wx.setStorageSync('expires_at', res.data.expires_at);
          wx.setStorageSync('username', res.data.user);
          wx.setStorageSync('token', res.data.token);

          // 根据用户选择保存账号和密码
          this.saveCredentialsIfNeeded();

          wx.reLaunch({ url: '/pages/seaSchedule/index' });
        } else {
          // 直接显示后端返回的错误信息（现在已经是中文）
          let errorMsg = res.data.message || '未知错误';
          
          wx.showToast({ 
            title: errorMsg, 
            icon: 'none',
            duration: 3000
          });
        }
      },
      fail: () => {
        wx.showToast({ title: '网络连接失败', icon: 'none' });
      }
    });
  },

  // 提交表单
  submitForm() {
    if (!this.validateForm()) return
    
    wx.showLoading({ title: '提交中...' })
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      method: 'POST',
      header: this.getHeaders(),
      data: this.prepareData(),
      complete: () => wx.hideLoading(), // 统一在这里关闭
      success: (res) => {
        if (res.data.status === 'success') {
          wx.showToast({ title: '提交成功', icon: 'success' })
          wx.navigateBack()
          this.refreshPreviousPage()
        } else {
          wx.showToast({ title: '提交失败: ' + (res.data.message || '未知错误'), icon: 'none' })
        }
      },
      fail: () => {
        wx.showToast({ title: '网络错误，请重试', icon: 'none' })
      }
    })
  },

  // 其他辅助方法
  changeStation(e) {
    const value = e.detail.value;
    this.setData({
      station: value,
      stationIndex: value === '一站' ? 0 : 1
    });
  },

  changeStartDate(e) { 
    this.setData({ startDate: e.detail.value }, this.calculateDays);
  },

  changeEndDate(e) { 
    this.setData({ endDate: e.detail.value }, this.calculateDays);
  },

  calculateDays() {
    if (this.data.startDate && this.data.endDate) {
      // 统一使用ISO格式处理
      const start = new Date(this.data.startDate.replace(/ /g, 'T'));
      const end = new Date(this.data.endDate.replace(/ /g, 'T'));
      const days = Math.ceil((end - start) / 86400000) + 1;
      this.setData({ days });
    }
  },


  // 新增提交后停留方法
  submitAndStay() {
    if (!this.validateForm()) return
    
    wx.showLoading({ title: '提交中...' })
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/shift_schedule_api.php',
      method: 'POST',
      header: this.getHeaders(),
      data: this.prepareData(),
      complete: () => wx.hideLoading(), // 统一在这里关闭
      success: (res) => {
        if (res.data.status === 'success') {
          wx.showToast({ title: '提交成功', icon: 'success' })
          this.resetForm()
          // 刷新上一页数据
          this.refreshPreviousPage()
        } else {
          wx.showToast({ title: '提交失败: ' + (res.data.message || '未知错误'), icon: 'none' })
        }
      },
      fail: () => {
        wx.showToast({ title: '网络错误，请重试', icon: 'none' })
      }
    })
  },

  // 辅助方法
  validateForm() {
    if (!this.data.startDate || !this.data.endDate) {
      wx.showToast({ title: '请填写完整日期', icon: 'none' })
      return false
    }
    return true
  },

  getHeaders() {
    return {
      'Authorization': wx.getStorageSync('token'),
      'Content-Type': 'application/json'
    }
  },

  prepareData() {
    return {
      action: this.data.editMode ? 'update_schedule' : 'add_schedule',
      station: this.data.station,
      startDate: this.data.startDate,
      endDate: this.data.endDate,
      id: this.data.editId,
      remark: this.data.remark,
      sort: 1  // 新增排序标识
    }
  },

  resetForm() {
    this.setData({
      startDate: '',
      endDate: '',
      days: 0,
      station: '一站',
      stationIndex: 0
    })
  },

  refreshPreviousPage() {
    const pages = getCurrentPages()
    if (pages.length > 1) {
      const prevPage = pages[pages.length - 2]
      // 使用setTimeout确保当前页面的loading已经结束
      setTimeout(() => {
        prevPage.onLoad()
      }, 100)
    }
  }
})