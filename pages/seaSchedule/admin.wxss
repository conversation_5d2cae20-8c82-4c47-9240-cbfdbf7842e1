/* admin.wxss */
.container {
  padding: 30rpx;
  background: #f5f7fa;
  min-height: 100vh;
}

/* 顶部标题栏 */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.form-title {
  font-size: 36rpx;
  color: #2c3e50;
  font-weight: bold;
}

.compact-login-btn {
  width: auto !important;
  padding: 12rpx 30rpx !important;
  font-size: 26rpx !important;
  background: #3498db !important;
  color: white !important;
  border-radius: 40rpx !important;
  line-height: 1.4;
}

/* 登录界面 */
.login-box {
  background: white;
  border-radius: 24rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.06);
}

/* 缩小天数卡片 */
.compact-days {
  padding: 20rpx !important;
  margin: 20rpx 0;
}

.modern-input {
  height: 80rpx;
  padding: 0 5rpx;
  margin: 15rpx 4%;
  background: #f8f9fa;
  border-radius: 16rpx;
  font-size: 28rpx;
  color: #2c3e50;
}

/* 记住功能样式 */
.remember-options {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 20rpx 0;
  padding: 0 20rpx;
}

.remember-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.remember-checkbox {
  margin-right: 12rpx;
  transform: scale(0.9);
}

.remember-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  user-select: none;
}

.login-btn {
  background: #27ae60 !important;
  color: white !important;
  margin-top: 30rpx;
  border-radius: 16rpx !important;
  font-size: 32rpx !important;
}

/* 表单内容 */
.card-style {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 10rpx 0;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
}

.item-label {
  font-size: 28rpx;
  color: #7f8c8d;
  margin-bottom: 12 rpx;
  display: block;
  padding: 8rpx 0 8rpx 15rpx !important; /* 增加左内边距 */
}

.modern-picker {
  width: 100%;
  margin: 0 15rpx;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15rpx 20;
}

.picker-text {
  font-size: 30rpx;
  margin-left: 10rpx;
  color: #2c3e50;
}

.picker-arrow {
  width: 32rpx;
  height: 32rpx;
  margin-right: 10rpx;
}

.picker-icon {
  width: 40rpx;
  height: 40rpx;
}

/* 天数显示 */
.days-card {
  background: #3498db;
  border-radius: 16rpx;
  padding: 25rpx;
  margin: 20rpx 0;
  text-align: center;
}

.days-label {
  font-size: 28rpx;
  color: rgba(255,255,255,0.9);
  display: block;
}

.days-value {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
  display: block;
  margin-top: 15rpx;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 30rpx;
  margin-top: 60rpx;
}

.submit-btn {
  flex: 1;
  border-radius: 12rpx !important;
  font-size: 30rpx !important;
  padding: 25rpx !important;
}

.primary {
  background: #27ae60 !important;
  color: white !important;
}

.secondary {
  background: #3498db !important;
  color: white !important;
}

/* 整体容器 */
.compact-container {
  padding: 30rpx 40rpx !important;
}

/* 登录框缩小 */
.compact-login {
  padding: 30rpx 25rpx !important;
  border-radius: 20rpx !important;
}

/* 输入框尺寸修正 */
.compact-input {
  height: 90rpx !important;
  font-size: 28rpx !important;
  width: 85% !important;  /* 解决右侧空白问题 */
  margin: 20rpx 4% !important;
  padding: 0 20rpx !important;
}


/* 表单项间距缩小 */
.compact-item {
  padding: 20rpx !important;
  margin: 15rpx 0 !important;
}

/* 天数卡片修正 */
.days-card {
  padding: 20rpx !important;
  margin: 20rpx 0 !important;
  border-radius: 10rpx !important;
}

.days-value {
  font-size: 40rpx !important;
  margin-top: 10rpx !important;
}

.compact-label {
  padding-left: 18rpx !important; /* 标签文字右移 */
  margin-bottom: 15rpx !important; /* 增加与选择器的间距 */
}

.station-group {
  display: flex;
  gap: 20px;
  margin-top: 10px;
}

.station-radio {
  display: flex;
  align-items: center;
  gap: 5px;
}

.station-radio text {
  font-size: 16px;
  color: #333;
}