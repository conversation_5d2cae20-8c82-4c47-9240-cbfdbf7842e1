<!-- pages/seaSchedule/index.wxml -->
<view class="container">
  <!-- 下次倒班时间横幅 -->
  <view class="next-shift-banner" bindtap="handleTitleClick">
    <!-- 倒班当天只显示"今日倒班"四个字 -->
    <view wx:if="{{isChangeDayToday}}" class="banner-date">今日倒班</view>
    <!-- 非倒班当天显示完整信息 -->
    <block wx:else>
      <view class="banner-title">
        <text class="banner-icon">🕐</text>
        <text>下次倒班时间</text>
      </view>
      <view wx:if="{{nextShift}}" class="banner-date">{{nextShift.start_date}}</view>
      <view wx:else class="banner-date">暂无数据</view>
      <view wx:if="{{nextShift}}" class="banner-countdown">距离下次倒班还有 {{nextShift.daysToNext}} 天</view>
      <view class="banner-current-info">
        <text wx:if="{{currentShift}}">当前班次: {{currentShift.station}} | 剩余时间: {{currentShift.remainingDays}}天</text>
        <text wx:else>暂无当前班次信息</text>
      </view>
    </block>
  </view>

  <!-- 年份选择和视图切换 -->
  <view class="schedule-card">
    <view class="card-content">
      <view class="header-controls">
        <view class="year-info-section">
          <picker
            mode="selector"
            range="{{yearRange}}"
            value="{{yearRange.length - 2}}"
            bindchange="changeYear"
          >
            <view class="compact-picker">{{year}}年</view>
          </picker>

        </view>

        <view class="view-mode-switcher">
          <view class="switch-btn {{viewMode === 'list' ? 'active' : ''}}" bindtap="switchToListMode">
            <text>列表</text>
          </view>
          <view class="switch-btn {{viewMode === 'calendar' ? 'active' : ''}}" bindtap="switchToCalendarMode">
            <text>日历</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 管理员面板 -->
  <view wx:if="{{isAdmin}}" class="admin-bar">
    <!-- 第一行：管理员信息 -->
    <view class="admin-info-row">
      <text class="admin-icon">🛡️</text>
      <text class="username">管理员: {{username}}</text>
    </view>

    <!-- 第二行：管理按钮 -->
    <view class="admin-actions">
      <button class="mini-btn add-btn" bindtap="navToAdmin">
        <text>➕ 新增</text>
      </button>
      <button class="mini-btn logout-btn" bindtap="handleLogout">
        <text>🚪 退出</text>
      </button>
    </view>
  </view>



  <!-- 列表模式 -->
  <scroll-view
    wx:if="{{viewMode === 'list'}}"
    scroll-y
    class="scroll-view"
    scroll-into-view="{{scrollIntoView}}"
    scroll-with-animation="true"
  >
    <view wx:for="{{12}}" wx:key="index" class="month-card" id="month-{{item+1}}">
      <view class="month-header">
        <view class="title-wrapper">
          <view class="month-title">
            <text class="month-icon">📅</text>
            <text>{{year}}年{{item+1}}月</text>
          </view>
          <text
            wx:if="{{remarks && remarks[item+1]}}"
            class="remark-badge"
          >{{remarks[item+1]}}</text>
        </view>
      </view>

      <view class="schedule-items">
        <block wx:for="{{schedules[item+1]}}" wx:key="id">
          <view
            class="record-item {{item.isActive ? 'active-record' : ''}} {{item.station==='二站' ? 'station-two' : ''}}"
          >
            <!-- 第一行：数据内容 -->
            <view class="data-row">
              <text class="station-tag {{item.station==='二站' ? 'station-two' : ''}}">{{item.station}}</text>
              <view class="schedule-details">
                <text class="date-range">{{item.start_date}} 至 {{item.end_date}}</text>
                <text class="days-info">{{item.days}}天 • {{item.status}}</text>
              </view>
              <view wx:if="{{item.status === '进行中'}}" class="status-badge">进行中</view>
            </view>

            <!-- 第二行：管理按钮 -->
            <view wx:if="{{isAdmin}}" class="action-btns">
              <button class="edit-btn" data-item="{{item}}" bindtap="navToEdit">编辑</button>
              <button class="delete-btn" data-id="{{item.id}}" bindtap="deleteRecord">删除</button>
            </view>
          </view>
        </block>
      </view>
      
      <view wx:if="{{schedules[item+1].length === 0}}" class="no-data">
        本月无记录
      </view>

      <!-- 月份管理按钮 -->
      <view wx:if="{{isAdmin}}" class="month-admin-btns">
        <button
          class="month-remark-btn"
          bindtap="navToRemark"
          data-year="{{year}}"
          data-month="{{item+1}}"
        >备注</button>
      </view>
    </view>

    <!-- 全年统计 -->
    <view class="total-card">
      <view class="total-title">
        <text class="total-icon">📊</text>
        <text>全年统计</text>
      </view>
      <view class="total-item first-station">
        <view class="total-label">
          <view class="station-dot"></view>
          <text>一站合计</text>
        </view>
        <text>{{total.first}}天</text>
      </view>
      <view class="total-item second-station">
        <view class="total-label">
          <view class="station-dot"></view>
          <text>二站合计</text>
        </view>
        <text>{{total.second}}天</text>
      </view>
    </view>

    <!-- 底部提示信息 -->
    <view class="footer-notice">
      <text class="notice-icon">⚠️</text>
      <text class="notice-text">倒班时间为计划时间，实际倒班时间由于天气等原因会有所不同，以实际通知为准。</text>
    </view>
  </scroll-view>

  <!-- 日历模式 -->
  <view wx:if="{{viewMode === 'calendar'}}" class="calendar-outer">
    <!-- 月份标题栏 带切换按钮 -->
    <view class="calendar-month-header">
      <view class="month-nav-button" bindtap="switchToPrevMonth">
        <text class="nav-arrow">◀</text>
      </view>
      <view class="calendar-month-title-container">
        <text class="calendar-month-title">{{year}}年{{displayMonth}}月</text>
        <text 
          wx:if="{{remarks && remarks[displayMonth]}}" 
          class="calendar-remark-text"
        >{{remarks[displayMonth]}}</text>
      </view>
      <view class="month-nav-button" bindtap="switchToNextMonth">
        <text class="nav-arrow">▶</text>
      </view>
    </view>

    <!-- 日历内容区域 -->
    <view class="calendar-content-area" 
      bindtouchstart="calendarTouchStart"
      bindtouchmove="calendarTouchMove"
      bindtouchend="calendarTouchEnd"
      bindtouchcancel="calendarTouchEnd">
      
      <scroll-view scroll-y class="calendar-scroll-view" scroll-with-animation="true">
        <!-- 单月日历卡片 -->
        <view class="calendar-month-card">
          <!-- 日历表头 - 确保横向显示 -->
          <view class="calendar-header-row">
            <view class="calendar-weekday" style="color:#e74c3c;">日</view>
            <view class="calendar-weekday">一</view>
            <view class="calendar-weekday">二</view>
            <view class="calendar-weekday">三</view>
            <view class="calendar-weekday">四</view>
            <view class="calendar-weekday">五</view>
            <view class="calendar-weekday" style="color:#e74c3c;">六</view>
          </view>

          <!-- 日历内容 -->
          <view class="calendar-body">
            <block wx:for="{{calendarData[displayMonth].days}}" wx:key="id" wx:for-item="day">
              <view class="calendar-cell {{day.currentMonth ? '' : 'other-month'}}">
                <view class="calendar-date {{day.isToday ? 'today-date' : ''}}">{{day.day}}</view>
                <!-- 不是换班日的普通班次（包括非当前月的日期） -->
                <block wx:if="{{day.shift && !day.isChangeDay}}">
                  <view class="calendar-shift-tag {{day.currentMonth ? (day.shift.station === '一站' ? 'first-station-bg' : 'second-station-bg') : 'other-month-shift'}}">
                    {{day.shift.station}}
                  </view>
                </block>
                <!-- 换班日只显示换班（包括非当前月的日期） -->
                <block wx:if="{{day.isChangeDay}}">
                  <view class="calendar-shift-tag {{day.currentMonth ? 'change-day-bg' : 'other-month-change'}}">
                    换班
                  </view>
                </block>
              </view>
            </block>
          </view>
        </view>
        
        <!-- 日历下方的颜色图例 -->
        <view class="calendar-legend">
          <view class="legend-item">
            <view class="legend-color first-station-bg"></view>
            <text class="legend-text">一站</text>
          </view>
          <view class="legend-item">
            <view class="legend-color second-station-bg"></view>
            <text class="legend-text">二站</text>
          </view>
          <view class="legend-item">
            <view class="legend-color change-day-bg"></view>
            <text class="legend-text">换班</text>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</view>