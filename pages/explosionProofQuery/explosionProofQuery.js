// 错误上报函数
function reportError(errorType, errorMessage, errorDetails = {}) {
  try {
    wx.request({
      url: 'https://sunxiyue.com/zdh/api/error_report.php',
      method: 'POST',
      data: {
        module: 'explosionProofQuery',
        errorType: errorType,
        errorMessage: errorMessage,
        errorDetails: errorDetails,
        timestamp: new Date().toISOString(),
        userAgent: wx.getSystemInfoSync()
      },
      fail: (err) => {
        // 错误上报失败时不影响用户体验，只在控制台记录
        console.warn('错误上报失败:', err);
      }
    });
  } catch (e) {
    console.warn('错误上报异常:', e);
  }
}

Page({
  data: {
    inputValue: '',
    showResult: false,
    parseResults: [],
    activeTab: 'type',
    showDetailModal: false,
    detailInfo: {},



    // OCR相关数据
    showImageModal: false,
    selectedImage: '',
    originalImagePath: '',
    isRecognizing: false,
    recognitionResult: '',

    // 区域选择相关数据
    cropMode: false,
    hasCropSelection: false,
    imageDisplayWidth: 0,
    imageDisplayHeight: 0,
    imageDisplayLeft: 0,
    imageDisplayTop: 0,
    originalImageWidth: 0,
    originalImageHeight: 0,
    cropBox: {
      left: 50,
      top: 50,
      width: 200,
      height: 150
    },
    isDragging: false,
    dragHandle: '',
    startTouch: { x: 0, y: 0 },
    startCropBox: { left: 0, top: 0, width: 0, height: 0 },

    // 常用示例
    examples: [
      // 新标准示例 (GB/T 3836-2021)
      {
        code: 'Ex db IIB T6 Gb',
        description: '新标准：隔爆型(1区/2区)，适用于乙烯等中等危险气体，表面温度≤85℃，高保护级别',
        standard: '新标准'
      },
      {
        code: 'Ex tb IIIC T80°C Db',
        description: '新标准：外壳保护型(高保护)，适用于导电性粉尘环境，表面温度≤80℃，高保护级别',
        standard: '新标准'
      },
      // 旧标准示例 (GB/T 3836-2010)
      {
        code: 'Ex d IIB T4',
        description: '旧标准：隔爆型，适用于乙烯等中等危险气体，表面温度≤135℃',
        standard: '旧标准'
      }
    ],

    // 参考数据
    referenceData: {
      type: [
        // 新标准防爆类型 (GB/T 3836-2021)
        {
          code: 'da',
          description: '隔爆型(0区)',
          colorClass: 'new-standard-color',
          detail: {
            title: '隔爆型 "da" (新标准)',
            content: '适用于EPL Ga或Ma的隔爆外壳，具有很高的保护等级，在正常运行、预期故障或罕见故障时不是点燃源。适用于0区气体环境或煤矿瓦斯环境。'
          }
        },
        {
          code: 'db',
          description: '隔爆型(1区/2区)',
          colorClass: 'new-standard-color',
          detail: {
            title: '隔爆型 "db" (新标准)',
            content: '适用于EPL Gb或Mb的隔爆外壳，具有高保护等级，在正常运行或预期故障条件下不是点燃源。适用于1区/2区气体环境或煤矿瓦斯环境。'
          }
        },
        {
          code: 'dc',
          description: '隔爆型(开关触点)',
          colorClass: 'new-standard-color',
          detail: {
            title: '隔爆型 "dc" (新标准)',
            content: '适用于EPL Gc的隔爆外壳，具有一般保护等级，在正常运行中不是点燃源。主要用于开关触点设备。'
          }
        },
        {
          code: 'eb',
          description: '增安型(高保护)',
          colorClass: 'new-standard-color',
          detail: {
            title: '增安型 "eb" (新标准)',
            content: '适用于EPL Gb或Mb的增安型设备，具有高保护等级，通过加强安全措施防止火花或高温，在正常运行或预期故障条件下不是点燃源。'
          }
        },
        {
          code: 'ec',
          description: '增安型(一般保护)',
          colorClass: 'new-standard-color',
          detail: {
            title: '增安型 "ec" (新标准)',
            content: '适用于EPL Gc的增安型设备，具有一般保护等级，在正常运行中不是点燃源。'
          }
        },
        {
          code: 'tb',
          description: '外壳保护型(高保护)',
          colorClass: 'new-standard-color',
          detail: {
            title: '外壳保护型 "tb" (新标准)',
            content: '适用于EPL Db的外壳保护型设备，具有高保护等级，专门用于粉尘环境，在正常运行或预期故障条件下不是点燃源。'
          }
        },
        {
          code: 'tc',
          description: '外壳保护型(一般保护)',
          colorClass: 'new-standard-color',
          detail: {
            title: '外壳保护型 "tc" (新标准)',
            content: '适用于EPL Dc的外壳保护型设备，具有一般保护等级，专门用于粉尘环境，在正常运行过程中不是点燃源。'
          }
        },
        {
          code: 'tD',
          description: '外壳保护型(粉尘)',
          colorClass: 'new-standard-color',
          detail: {
            title: '外壳保护型 "tD" (新标准)',
            content: '适用于粉尘环境的外壳保护型设备，通过外壳保护避免粉尘或粉尘云被点燃。专门用于可燃性粉尘环境，常与区域级别（如A21、B21）配合使用。'
          }
        },
        {
          code: 'mD',
          description: '浇封型(粉尘)',
          colorClass: 'new-standard-color',
          detail: {
            title: '浇封型 "mD" (新标准)',
            content: '适用于粉尘环境的浇封型设备，通过浇封材料保护电气部件，防止粉尘进入和点燃。专门用于可燃性粉尘环境。'
          }
        },
        {
          code: 'nL',
          description: '限制能量型',
          colorClass: 'new-standard-color',
          detail: {
            title: '限制能量型 "nL" (新标准)',
            content: '通过限制电火花的能量和表面温度来防止点燃，适用于特定的防爆环境。'
          }
        },
        {
          code: 'nZ',
          description: '隔离型',
          colorClass: 'new-standard-color',
          detail: {
            title: '隔离型 "nZ" (新标准)',
            content: '通过隔离可燃性气体来防止点燃，适用于特定的防爆环境。'
          }
        },
        // 旧标准防爆类型 (GB/T 3836-2010)
        {
          code: 'd',
          description: '隔爆型(旧标准)',
          colorClass: 'old-standard-color',
          detail: {
            title: '隔爆型 "Exd" (旧标准)',
            content: '一种外壳内允许爆炸性混合物爆炸，并能承受爆炸压力而不损坏，使之在规定的使用条件下，壳内产生的电弧、传播的火焰，外壳里爆炸产生的过热均不能点燃周围爆炸性混合物的电气设备。'
          }
        },
        {
          code: 'e',
          description: '增安型(旧标准)',
          colorClass: 'old-standard-color',
          detail: {
            title: '增安型 "Exe" (旧标准)',
            content: '一种对普通设备采取一些附加措施以提高其安全程度，在正常运行和认可的过载状况下的设备不会产生点燃周围的爆炸性混合物的电气设备。'
          }
        },
        {
          code: 'ia',
          description: '本安型(a级)',
          detail: {
            title: '本质安全型 "Exi"',
            content: '内部的所有电路都是本质安全电路的电气设备，本质安全电路的是在标准规定条件（包括正常工作和规定的故障条件）下产生的任何电火花或任何热效应均不能点燃规定的爆炸性气体环境的电路。'
          }
        },
        {
          code: 'ib',
          description: '本安型(b级)',
          detail: {
            title: '本质安全型 "Exi"',
            content: '内部的所有电路都是本质安全电路的电气设备，本质安全电路的是在标准规定条件（包括正常工作和规定的故障条件）下产生的任何电火花或任何热效应均不能点燃规定的爆炸性气体环境的电路。'
          }
        },
        {
          code: 'ic',
          description: '本安型(c级)',
          detail: {
            title: '本质安全型 "Exi"',
            content: '内部的所有电路都是本质安全电路的电气设备，本质安全电路的是在标准规定条件（包括正常工作和规定的故障条件）下产生的任何电火花或任何热效应均不能点燃规定的爆炸性气体环境的电路。'
          }
        },
        {
          code: 'p',
          description: '正压型',
          detail: {
            title: '正压外壳型 "Exp"',
            content: '具有正压外壳的电气设备，即该外壳能保持其内部气体的压力高于外部环境大气压力，且能阻止外部爆炸性混合物的进入。'
          }
        },
        {
          code: 'o',
          description: '油浸型',
          detail: {
            title: '油浸型 "Exo"',
            content: '一种将电气设备或电气设备的部件全部浸在保护液中，使设备不能够点燃液面以上或外壳外面的爆炸性混合物的电气设备。'
          }
        },
        {
          code: 'q',
          description: '充砂型',
          detail: {
            title: '充砂型',
            content: '一种外壳内充填砂粒或其他规定特性的粉粒材料，使得在规定的使用条件下，壳内产生的电弧或高温均不能点燃周围爆炸性混合物的电气设备。'
          }
        },
        {
          code: 'm',
          description: '浇封型',
          detail: {
            title: '浇封型',
            content: '将可能点燃爆炸性环境的部件浇封在浇封剂中，使其不能点燃周围爆炸性混合物的电气设备。'
          }
        },
        {
          code: 'n',
          description: 'n型',
          detail: {
            title: 'n型 "Exn"',
            content: '一种在正常运行时或规定的制造厂规定的异常条件下，不会产生引起点燃周围爆炸性混合物的电气设备。'
          }
        }
      ],
      gas: [
        {
          code: 'I',
          description: '煤矿井下',
          colorClass: 'gas-color',
          detail: {
            title: 'I类 - 煤矿井下',
            content: '适用于煤矿井下有甲烷和煤尘爆炸危险的场所。这类环境具有特殊的爆炸性气体成分，主要是甲烷气体，需要专门的防爆措施和设备。'
          }
        },
        {
          code: 'IIA',
          description: '丙烷类气体',
          colorClass: 'gas-color',
          detail: {
            title: 'IIA类 - 丙烷类气体',
            content: '适用于丙烷、戊烷、汽油、乙醇、乙醛、丙酮等爆炸性气体环境。这类气体的最大试验安全间隙(MESG)≥0.9mm，最小点燃电流比(MICR)≥0.8，爆炸危险性相对较低。'
          }
        },
        {
          code: 'IIB',
          description: '乙烯类气体',
          colorClass: 'gas-color',
          detail: {
            title: 'IIB类 - 乙烯类气体',
            content: '适用于乙烯、乙醚、环丙烷等爆炸性气体环境。这类气体的最大试验安全间隙(MESG)为0.5-0.9mm，最小点燃电流比(MICR)为0.45-0.8，爆炸危险性中等。'
          }
        },
        {
          code: 'IIC',
          description: '氢气类气体',
          colorClass: 'gas-color',
          detail: {
            title: 'IIC类 - 氢气类气体',
            content: '适用于氢气、乙炔、二硫化碳等爆炸性气体环境。这类气体的最大试验安全间隙(MESG)<0.5mm，最小点燃电流比(MICR)<0.45，爆炸危险性最高，对防爆设备要求最严格。'
          }
        },
        // 新标准粉尘分类 (GB/T 3836-2021)
        {
          code: 'IIIA',
          description: '可燃性飞絮',
          colorClass: 'new-standard-color',
          detail: {
            title: 'IIIA类 - 可燃性飞絮 (新标准)',
            content: '适用于可燃性飞絮环境，如棉花、亚麻、人造纤维等纤维状可燃物质。这类物质容易悬浮在空气中形成爆炸性混合物，需要特殊的防爆措施。'
          }
        },
        {
          code: 'IIIB',
          description: '非导电性粉尘',
          colorClass: 'new-standard-color',
          detail: {
            title: 'IIIB类 - 非导电性粉尘 (新标准)',
            content: '适用于非导电性粉尘环境，如面粉、淀粉、煤粉、木粉、塑料粉等。这类粉尘不导电，但在一定条件下仍可能引起爆炸，需要防止粉尘积聚和点燃源。'
          }
        },
        {
          code: 'IIIC',
          description: '导电性粉尘',
          colorClass: 'new-standard-color',
          detail: {
            title: 'IIIC类 - 导电性粉尘 (新标准)',
            content: '适用于导电性粉尘环境，如铝粉、镁粉、锌粉、炭黑等金属粉尘。这类粉尘具有导电性，爆炸危险性最高，对防爆设备的要求最为严格。'
          }
        },
        // 旧标准粉尘分类 (GB/T 3836-2010)
        {
          code: 'III',
          description: '粉尘环境(旧标准)',
          colorClass: 'old-standard-color',
          detail: {
            title: 'III类 - 粉尘环境 (旧标准)',
            content: '适用于可燃性粉尘环境的通用标识。旧标准未细分粉尘类型，新标准已细分为IIIA、IIIB、IIIC三个子类，分别对应飞絮、非导电粉尘和导电粉尘。'
          }
        }
      ],
      temp: [
        {
          code: 'T1',
          description: '≤450℃',
          colorClass: 'temp-color',
          detail: {
            title: 'T1级 - 最高表面温度≤450℃',
            content: '设备表面温度不超过450℃。适用于引燃温度高于450℃的爆炸性气体环境，如氨气(引燃温度630℃)等。这是温度等级中要求最宽松的级别。'
          }
        },
        {
          code: 'T2',
          description: '≤300℃',
          colorClass: 'temp-color',
          detail: {
            title: 'T2级 - 最高表面温度≤300℃',
            content: '设备表面温度不超过300℃。适用于引燃温度在300-450℃之间的爆炸性气体环境，如甲烷(引燃温度537℃)、丙烷(引燃温度466℃)等。'
          }
        },
        {
          code: 'T3',
          description: '≤200℃',
          colorClass: 'temp-color',
          detail: {
            title: 'T3级 - 最高表面温度≤200℃',
            content: '设备表面温度不超过200℃。适用于引燃温度在200-300℃之间的爆炸性气体环境，如汽油蒸气(引燃温度220-280℃)、柴油蒸气等。'
          }
        },
        {
          code: 'T4',
          description: '≤135℃',
          colorClass: 'temp-color',
          detail: {
            title: 'T4级 - 最高表面温度≤135℃',
            content: '设备表面温度不超过135℃。适用于引燃温度在135-200℃之间的爆炸性气体环境，如乙醛(引燃温度175℃)、乙醚(引燃温度160℃)等。'
          }
        },
        {
          code: 'T5',
          description: '≤100℃',
          colorClass: 'temp-color',
          detail: {
            title: 'T5级 - 最高表面温度≤100℃',
            content: '设备表面温度不超过100℃。适用于引燃温度在100-135℃之间的爆炸性气体环境，如二硫化碳(引燃温度102℃)等低引燃温度的危险气体。'
          }
        },
        {
          code: 'T6',
          description: '≤85℃',
          colorClass: 'temp-color',
          detail: {
            title: 'T6级 - 最高表面温度≤85℃',
            content: '设备表面温度不超过85℃。这是最严格的温度等级，适用于引燃温度极低的爆炸性气体环境，对设备的温度控制要求最高。'
          }
        },
        // 新标准粉尘环境具体温度值示例 (GB/T 3836-2021)
        {
          code: 'T80°C',
          description: '≤80℃(粉尘)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'T80°C - 粉尘环境温度标识 (新标准)',
            content: '设备表面温度不超过80℃，专用于粉尘环境。新标准GB/T 3836-2021要求粉尘环境使用具体温度值标识，如T80°C、T120°C、T200°C等，更加精确地控制设备表面温度。'
          }
        },
        {
          code: 'T200°C',
          description: '≤200℃(粉尘)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'T200°C - 粉尘环境温度标识 (新标准)',
            content: '设备表面温度不超过200℃，专用于粉尘环境。适用于引燃温度较高的粉尘环境，如某些金属粉尘或有机粉尘。'
          }
        },
        {
          code: 'T320°C',
          description: '≤320℃(粉尘)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'T320°C - 粉尘环境温度标识 (新标准)',
            content: '设备表面温度不超过320℃，专用于粉尘环境。适用于引燃温度很高的特殊粉尘环境，对应EPL Da级别的高温度要求。'
          }
        }
      ],
      level: [
        {
          code: 'Ga',
          description: '0区气体环境',
          colorClass: 'level-color',
          detail: {
            title: 'Ga级 - 0区气体环境',
            content: '适用于0区气体环境，即连续出现或长期出现爆炸性气体混合物的环境。这是最高危险级别，要求设备具有最高的安全性能，通常只有本质安全型设备才能在此环境使用。'
          }
        },
        {
          code: 'Gb',
          description: '1区气体环境',
          colorClass: 'level-color',
          detail: {
            title: 'Gb级 - 1区气体环境',
            content: '适用于1区气体环境，即在正常运行时可能出现爆炸性气体混合物的环境。这是中等危险级别，隔爆型、增安型、本质安全型等多种防爆型式的设备都可以使用。'
          }
        },
        {
          code: 'Gc',
          description: '2区气体环境',
          colorClass: 'level-color',
          detail: {
            title: 'Gc级 - 2区气体环境',
            content: '适用于2区气体环境，即在正常运行时不可能出现爆炸性气体混合物，如果出现也仅是短时间存在的环境。这是相对较低的危险级别，多种防爆型式的设备都适用。'
          }
        },
        {
          code: 'Da',
          description: '20区粉尘环境',
          colorClass: 'level-color',
          detail: {
            title: 'Da级 - 20区粉尘环境',
            content: '适用于20区粉尘环境，即空气中可燃性粉尘连续出现或长期出现或频繁出现的场所。这是粉尘环境中的最高危险级别，对防爆设备要求最严格。'
          }
        },
        {
          code: 'Db',
          description: '21区粉尘环境',
          colorClass: 'level-color',
          detail: {
            title: 'Db级 - 21区粉尘环境',
            content: '适用于21区粉尘环境，即在正常运行过程中，可能出现可燃性粉尘的场所。这是粉尘环境中的中等危险级别，需要采用相应的防爆措施。'
          }
        },
        {
          code: 'Dc',
          description: '22区粉尘环境',
          colorClass: 'level-color',
          detail: {
            title: 'Dc级 - 22区粉尘环境',
            content: '适用于22区粉尘环境，即在正常运行过程中不可能出现可燃性粉尘，如果出现也仅是短时间存在的场所。这是粉尘环境中相对较低的危险级别。'
          }
        },
        // 煤矿环境保护级别 (新标准 GB/T 3836-2021)
        {
          code: 'Ma',
          description: '煤矿瓦斯环境(很高保护)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'Ma级 - 煤矿瓦斯环境很高保护级别 (新标准)',
            content: '适用于煤矿瓦斯爆炸性环境，具有很高的保护等级。该级别具有足够的安全性，使设备在正常运行、出现预期故障或罕见故障，甚至在气体突然出现设备仍带电的情况下均不可能成为点燃源。'
          }
        },
        {
          code: 'Mb',
          description: '煤矿瓦斯环境(高保护)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'Mb级 - 煤矿瓦斯环境高保护级别 (新标准)',
            content: '适用于煤矿瓦斯爆炸性环境，具有高保护等级。该级别具有足够的安全性，使设备在正常运行中或在气体突然出现和设备断电之间的时间内出现的预期故障条件下不可能成为点燃源。'
          }
        },

        // 粉尘区域保护级别 (新标准 GB/T 3836-2021)
        {
          code: 'A20',
          description: '20区粉尘环境(平面结合)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'A20级 - 20区粉尘环境保护级别 (新标准)',
            content: '适用于20区粉尘环境，即连续出现或经常出现可燃性粉尘的场所。A表示平面结合面的防爆结构，20表示最高危险级别的粉尘环境，通常不允许电器设备进入。'
          }
        },
        {
          code: 'A21',
          description: '21区粉尘环境(平面结合)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'A21级 - 21区粉尘环境保护级别 (新标准)',
            content: '适用于21区粉尘环境，即在正常运行过程中可能出现可燃性粉尘的场所。A表示平面结合面的防爆结构，这是粉尘防爆电机的最高实用等级。'
          }
        },
        {
          code: 'A22',
          description: '22区粉尘环境(平面结合)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'A22级 - 22区粉尘环境保护级别 (新标准)',
            content: '适用于22区粉尘环境，即在异常情况下可燃性粉尘云偶尔出现的场所。A表示平面结合面的防爆结构，危险级别相对较低。'
          }
        },
        {
          code: 'B20',
          description: '20区粉尘环境(直口结合)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'B20级 - 20区粉尘环境保护级别 (新标准)',
            content: '适用于20区粉尘环境，即连续出现或经常出现可燃性粉尘的场所。B表示直口结合面的防爆结构，比A型更安全，但20区通常不允许电器设备进入。'
          }
        },
        {
          code: 'B21',
          description: '21区粉尘环境(直口结合)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'B21级 - 21区粉尘环境保护级别 (新标准)',
            content: '适用于21区粉尘环境，即在正常运行过程中可能出现可燃性粉尘的场所。B表示直口结合面的防爆结构，比A型更安全，这是粉尘防爆电机的最高实用等级。'
          }
        },
        {
          code: 'B22',
          description: '22区粉尘环境(直口结合)',
          colorClass: 'new-standard-color',
          detail: {
            title: 'B22级 - 22区粉尘环境保护级别 (新标准)',
            content: '适用于22区粉尘环境，即在异常情况下可燃性粉尘云偶尔出现的场所。B表示直口结合面的防爆结构，比A型更安全，危险级别相对较低。'
          }
        }
      ]
    },

    currentReferenceData: []
  },

  onLoad() {
    // 默认显示防爆类型参考数据
    this.setData({
      currentReferenceData: this.data.referenceData.type
    });
  },

  // 输入框内容变化
  onInputChange(e) {
    this.setData({
      inputValue: e.detail.value
    });
  },

  // 解析防爆标识
  parseExplosionProof() {
    const input = this.data.inputValue.trim();

    if (!input) {
      wx.showToast({
        title: '请输入防爆标识',
        icon: 'none'
      });
      return;
    }

    // 检查是否为复合标识（包含斜杠分隔的多个标识）
    if (input.includes('/')) {
      this.parseCompoundExplosionProof(input);
    } else {
      this.parseSingleExplosionProof(input);
    }
  },

  // 解析复合防爆标识
  parseCompoundExplosionProof(input) {
    const parts = input.split('/').map(part => part.trim());
    const allResults = [];

    parts.forEach((part, index) => {
      if (part) {
        const partResults = this.parseSingleExplosionProofInternal(part);
        if (partResults.length > 0) {
          // 判断标准类型
          const standardInfo = this.determineStandard(part);

          // 添加标准类型信息
          allResults.push({
            label: standardInfo.label,
            value: '',
            isStandardInfo: true,
            standardType: standardInfo.type
          });

          // 为每个部分添加标题
          allResults.push({
            label: `第${index + 1}个防爆标识 (${part})`,
            value: `适用于不同的爆炸性环境`,
            isTitle: true,
            colorClass: 'compound-title',
            standardBadge: standardInfo.badge
          });
          allResults.push(...partResults);

          // 添加分隔线（除了最后一个）
          if (index < parts.length - 1) {
            allResults.push({
              label: '',
              value: '',
              isSeparator: true
            });
          }
        }
      }
    });

    if (allResults.length === 0) {
      wx.showToast({
        title: '无法识别该防爆标识',
        icon: 'none'
      });
      return;
    }

    this.setData({
      parseResults: allResults,
      showResult: true
    });
  },

  // 解析单个防爆标识
  parseSingleExplosionProof(input) {
    const results = this.parseSingleExplosionProofInternal(input);

    if (results.length === 0) {
      wx.showToast({
        title: '无法识别该防爆标识',
        icon: 'none'
      });
      return;
    }

    // 判断标准类型并添加到结果前面
    const standardInfo = this.determineStandard(input);
    const finalResults = [
      {
        label: standardInfo.label,
        value: '',
        isStandardInfo: true,
        standardType: standardInfo.type
      },
      ...results
    ];

    this.setData({
      parseResults: finalResults,
      showResult: true
    });
  },

  // 解析单个防爆标识的内部逻辑
  parseSingleExplosionProofInternal(input) {
    const upperInput = input.trim().toUpperCase();

    // 检查是否以Ex开头
    if (!upperInput.startsWith('EX')) {
      return [];
    }

    const results = [];

    // 解析Ex标识
    results.push({
      label: 'Ex - 防爆标识',
      value: '表示该设备符合防爆标准'
    });

    // 移除Ex前缀，分析剩余部分
    let remaining = upperInput.substring(2).trim();

    // 解析防爆类型
    const typeResult = this.parseType(remaining);
    if (typeResult.found) {
      results.push(typeResult.result);
      remaining = typeResult.remaining;
    }

    // 解析设备类别和气体组别
    const gasResult = this.parseGasGroup(remaining);
    if (gasResult.found) {
      results.push(gasResult.result);
      remaining = gasResult.remaining;
    }

    // 先尝试解析保护级别（可能在温度之前，如A21）
    const levelResult = this.parseProtectionLevel(remaining);
    if (levelResult.found) {
      results.push(levelResult.result);
      remaining = levelResult.remaining;
    }

    // 解析IP防护等级
    const ipResult = this.parseIPRating(remaining);
    if (ipResult.found) {
      results.push(ipResult.result);
      remaining = ipResult.remaining;
    }

    // 解析温度组别
    const tempResult = this.parseTemperature(remaining);
    if (tempResult.found) {
      results.push(tempResult.result);
      remaining = tempResult.remaining;
    }

    // 如果之前没有解析到保护级别，再次尝试
    if (!levelResult.found) {
      const levelResult2 = this.parseProtectionLevel(remaining);
      if (levelResult2.found) {
        results.push(levelResult2.result);
      }
    }

    return results;
  },

  // 判断防爆标识的标准类型
  determineStandard(input) {
    const upperInput = input.trim().toUpperCase();

    // 新标准特征：包含EPL等级（Ga, Gb, Gc, Da, Db, Dc, Ma, Mb）
    const hasEPL = /\b[GDM][ABC]\b/i.test(upperInput);

    // 新标准特征：包含区域保护级别（A20, A21, A22, B20, B21, B22）
    const hasAreaLevel = /\b[AB][20-22]\b/i.test(upperInput);

    // 新标准特征：包含具体温度值（如T80°C）
    const hasSpecificTemp = /T\d+°C/i.test(upperInput);

    // 新标准特征：粉尘防爆型式（ta, tb, tc）
    const hasDustType = /\b(ta|tb|tc)\b/i.test(upperInput);

    // 旧标准特征：只有温度组别（T1-T6）且没有EPL等级和区域级别
    const hasOnlyTempGroup = /\bT[1-6]\b/i.test(upperInput) && !hasEPL && !hasAreaLevel;

    if (hasEPL || hasAreaLevel || hasSpecificTemp || hasDustType) {
      return {
        type: 'new',
        label: '新标准 (GB/T 3836-2021)',
        badge: '新标准'
      };
    } else if (hasOnlyTempGroup) {
      return {
        type: 'old',
        label: '旧标准 (GB/T 3836-2010)',
        badge: '旧标准'
      };
    } else {
      // 默认判断为新标准
      return {
        type: 'new',
        label: '新标准 (GB/T 3836-2021)',
        badge: '新标准'
      };
    }
  },

  // 解析防爆类型
  parseType(input) {
    // 新标准防爆类型 (GB/T 3836-2021)
    const newTypes = {
      'DA': { name: 'da - 隔爆型(0区)', desc: '隔爆外壳，适用于EPL Ga或Ma', standard: '新标准' },
      'DB': { name: 'db - 隔爆型(1区/2区)', desc: '隔爆外壳，适用于EPL Gb或Mb', standard: '新标准' },
      'DC': { name: 'dc - 隔爆型(开关触点)', desc: '隔爆外壳，适用于EPL Gc', standard: '新标准' },
      'EB': { name: 'eb - 增安型(高保护)', desc: '增安型，适用于EPL Gb或Mb', standard: '新标准' },
      'EC': { name: 'ec - 增安型(一般保护)', desc: '增安型，适用于EPL Gc', standard: '新标准' },
      'IA': { name: 'ia - 本质安全型(很高保护)', desc: '本质安全型，适用于EPL Ga或Ma', standard: '新标准' },
      'IB': { name: 'ib - 本质安全型(高保护)', desc: '本质安全型，适用于EPL Gb或Mb', standard: '新标准' },
      'IC': { name: 'ic - 本质安全型(一般保护)', desc: '本质安全型，适用于EPL Gc', standard: '新标准' },
      'MA': { name: 'ma - 浇封型(很高保护)', desc: '浇封型，适用于EPL Ga或Ma', standard: '新标准' },
      'MB': { name: 'mb - 浇封型(高保护)', desc: '浇封型，适用于EPL Gb或Mb', standard: '新标准' },
      'MC': { name: 'mc - 浇封型(一般保护)', desc: '浇封型，适用于EPL Gc', standard: '新标准' },
      'NA': { name: 'nA - 无火花型', desc: '无火花型，适用于EPL Gc', standard: '新标准' },
      'NC': { name: 'nC - 火花保护型', desc: '火花保护型，适用于EPL Gc', standard: '新标准' },
      'NR': { name: 'nR - 限制呼吸型', desc: '限制呼吸型，适用于EPL Gc', standard: '新标准' },
      'OB': { name: 'ob - 液浸型(高保护)', desc: '液浸型，适用于EPL Gb或Mb', standard: '新标准' },
      'OC': { name: 'oc - 液浸型(一般保护)', desc: '液浸型，适用于EPL Gc', standard: '新标准' },
      'PXB': { name: 'pxb - 正压型(高保护)', desc: '正压型，适用于EPL Gb或Mb', standard: '新标准' },
      'PYB': { name: 'pyb - 正压型(高保护)', desc: '正压型，适用于EPL Gb', standard: '新标准' },
      'PZC': { name: 'pzc - 正压型(一般保护)', desc: '正压型，适用于EPL Gc', standard: '新标准' },
      'TA': { name: 'ta - 外壳保护型(很高保护)', desc: '外壳保护型，适用于EPL Da', standard: '新标准' },
      'TB': { name: 'tb - 外壳保护型(高保护)', desc: '外壳保护型，适用于EPL Db', standard: '新标准' },
      'TC': { name: 'tc - 外壳保护型(一般保护)', desc: '外壳保护型，适用于EPL Dc', standard: '新标准' },
      'TD': { name: 'tD - 外壳保护型(粉尘)', desc: '外壳保护型，专用于粉尘环境', standard: '新标准' },
      'MD': { name: 'mD - 浇封型(粉尘)', desc: '浇封型，专用于粉尘环境', standard: '新标准' },
      'NL': { name: 'nL - 限制能量型', desc: '限制电火花的能量和表面温度', standard: '新标准' },
      'NZ': { name: 'nZ - 隔离型', desc: '隔离可燃性气体', standard: '新标准' }
    };

    // 旧标准防爆类型 (GB/T 3836-2010)
    const oldTypes = {
      'D': { name: 'd - 隔爆型', desc: '爆炸气体在壳体内被隔离', standard: '旧标准' },
      'E': { name: 'e - 增安型', desc: '通过加强安全措施防止火花或高温', standard: '旧标准' },
      'I': { name: 'i - 本质安全型', desc: '限制能量，避免引燃爆炸性环境', standard: '旧标准' },
      'P': { name: 'p - 正压型', desc: '内部充入保护气体防止外部爆炸物进入', standard: '旧标准' },
      'O': { name: 'o - 油浸型', desc: '电气部件浸在油中', standard: '旧标准' },
      'Q': { name: 'q - 充砂型', desc: '电气部件埋在砂中', standard: '旧标准' },
      'M': { name: 'm - 浇封型', desc: '电路被密封在材料中', standard: '旧标准' },
      'N': { name: 'n - n型', desc: 'n型防爆', standard: '旧标准' }
    };

    // 特殊处理：先检查大小写混合的类型
    const specialCases = ['tD', 'mD', 'nL', 'nZ'];
    for (const specialCase of specialCases) {
      if (input.length >= specialCase.length && input.substring(0, specialCase.length) === specialCase) {
        const upperCase = specialCase.toUpperCase();
        if (newTypes[upperCase]) {
          return {
            found: true,
            result: {
              label: newTypes[upperCase].name,
              value: newTypes[upperCase].desc + ' (' + newTypes[upperCase].standard + ')'
            },
            remaining: input.substring(specialCase.length).trim()
          };
        }
      }
    }

    // 优先检查新标准的多字符类型（3-4个字符）
    for (let len = 4; len >= 2; len--) {
      if (input.length >= len) {
        const chars = input.substring(0, len).toUpperCase();
        if (newTypes[chars]) {
          return {
            found: true,
            result: {
              label: newTypes[chars].name,
              value: newTypes[chars].desc + ' (' + newTypes[chars].standard + ')'
            },
            remaining: input.substring(len).trim()
          };
        }
      }
    }

    // 检查旧标准的单字符类型
    if (input.length >= 1) {
      const oneChar = input.substring(0, 1).toUpperCase();
      if (oldTypes[oneChar]) {
        return {
          found: true,
          result: {
            label: oldTypes[oneChar].name,
            value: oldTypes[oneChar].desc + ' (' + oldTypes[oneChar].standard + ')'
          },
          remaining: input.substring(1).trim()
        };
      }
    }

    return { found: false };
  },

  // 解析设备类别和气体/粉尘组别
  parseGasGroup(input) {
    const groups = {
      // 煤矿环境
      'I': { name: 'I类 - 煤矿井下', desc: '适用于煤矿瓦斯环境（甲烷气体）', standard: '通用' },

      // 气体环境 (新旧标准通用)
      'IIA': { name: 'IIA类 - 气体环境', desc: '适用于丙烷等低危险性气体，MESG≥0.9mm，MIC比值≥0.8', standard: '通用' },
      'IIB': { name: 'IIB类 - 气体环境', desc: '适用于乙烯等中等危险性气体，MESG:0.5-0.9mm，MIC比值:0.45-0.8', standard: '通用' },
      'IIC': { name: 'IIC类 - 气体环境', desc: '适用于氢气、乙炔等高危险性气体，MESG<0.5mm，MIC比值<0.45', standard: '通用' },

      // 粉尘环境 (新标准 GB/T 3836-2021)
      'IIIA': { name: 'IIIA类 - 粉尘环境', desc: '适用于可燃性飞絮环境（如棉花、亚麻等纤维）', standard: 'GB/T 3836-2021' },
      'IIIB': { name: 'IIIB类 - 粉尘环境', desc: '适用于非导电性粉尘环境（如面粉、淀粉、煤粉等）', standard: 'GB/T 3836-2021' },
      'IIIC': { name: 'IIIC类 - 粉尘环境', desc: '适用于导电性粉尘环境（如铝粉、镁粉、炭黑等）', standard: 'GB/T 3836-2021' },

      // 通用粉尘标识 (旧标准兼容)
      'III': { name: 'III类 - 粉尘环境', desc: '适用于可燃性粉尘环境（旧标准通用标识）', standard: 'GB/T 3836-2010' }
    };

    // 按长度优先检查，从4字符到1字符
    for (let len = 4; len >= 1; len--) {
      if (input.length >= len) {
        const chars = input.substring(0, len).toUpperCase();
        if (groups[chars]) {
          return {
            found: true,
            result: {
              label: groups[chars].name,
              value: groups[chars].desc + ' (' + groups[chars].standard + ')'
            },
            remaining: input.substring(len).trim()
          };
        }
      }
    }

    return { found: false };
  },

  // 解析温度组别/温度标识
  parseTemperature(input) {
    // 气体环境温度组别 (新旧标准通用)
    const gasTemps = {
      'T1': { name: 'T1 - 气体环境温度组别', desc: '设备表面温度 ≤450℃', standard: '通用' },
      'T2': { name: 'T2 - 气体环境温度组别', desc: '设备表面温度 ≤300℃', standard: '通用' },
      'T3': { name: 'T3 - 气体环境温度组别', desc: '设备表面温度 ≤200℃', standard: '通用' },
      'T4': { name: 'T4 - 气体环境温度组别', desc: '设备表面温度 ≤135℃', standard: '通用' },
      'T5': { name: 'T5 - 气体环境温度组别', desc: '设备表面温度 ≤100℃', standard: '通用' },
      'T6': { name: 'T6 - 气体环境温度组别', desc: '设备表面温度 ≤85℃', standard: '通用' }
    };

    // 首先检查新标准的具体温度值格式 (如 T80°C, T200°C)
    const specificTempRegex = /^T(\d+)°?C?/i;
    const specificMatch = input.match(specificTempRegex);

    if (specificMatch) {
      const tempValue = specificMatch[1];
      const fullMatch = specificMatch[0];

      return {
        found: true,
        result: {
          label: `${fullMatch} - 粉尘环境温度标识`,
          value: `设备表面温度 ≤${tempValue}℃ (新标准GB/T 3836-2021粉尘环境专用)`
        },
        remaining: input.substring(fullMatch.length).trim()
      };
    }

    // 检查传统温度组别 (T1-T6)
    if (input.length >= 2) {
      const tempCode = input.substring(0, 2).toUpperCase();
      if (gasTemps[tempCode]) {
        return {
          found: true,
          result: {
            label: gasTemps[tempCode].name,
            value: gasTemps[tempCode].desc + ' (' + gasTemps[tempCode].standard + ')'
          },
          remaining: input.substring(2).trim()
        };
      }
    }

    return { found: false };
  },

  // 解析保护级别 (EPL - Equipment Protection Level)
  parseProtectionLevel(input) {
    const levels = {
      // 气体环境保护级别
      'GA': { name: 'Ga - 气体环境保护级别', desc: '很高保护级别，适用于0区气体环境，正常运行、预期故障或罕见故障时不是点燃源', standard: 'GB/T 3836-2021' },
      'GB': { name: 'Gb - 气体环境保护级别', desc: '高保护级别，适用于1区气体环境，正常运行或预期故障条件下不是点燃源', standard: 'GB/T 3836-2021' },
      'GC': { name: 'Gc - 气体环境保护级别', desc: '一般保护级别，适用于2区气体环境，正常运行中不是点燃源', standard: 'GB/T 3836-2021' },

      // 粉尘环境保护级别
      'DA': { name: 'Da - 粉尘环境保护级别', desc: '很高保护级别，适用于20区粉尘环境，正常运行、预期故障或罕见故障时不是点燃源', standard: 'GB/T 3836-2021' },
      'DB': { name: 'Db - 粉尘环境保护级别', desc: '高保护级别，适用于21区粉尘环境，正常运行或预期故障条件下不是点燃源', standard: 'GB/T 3836-2021' },
      'DC': { name: 'Dc - 粉尘环境保护级别', desc: '一般保护级别，适用于22区粉尘环境，正常运行过程中不是点燃源', standard: 'GB/T 3836-2021' },

      // 煤矿环境保护级别
      'MA': { name: 'Ma - 煤矿环境保护级别', desc: '很高保护级别，适用于煤矿瓦斯环境，正常运行、预期故障或罕见故障时不是点燃源', standard: 'GB/T 3836-2021' },
      'MB': { name: 'Mb - 煤矿环境保护级别', desc: '高保护级别，适用于煤矿瓦斯环境，正常运行或预期故障条件下不是点燃源', standard: 'GB/T 3836-2021' },

      // 粉尘区域保护级别 (新标准 GB/T 3836-2021)
      'A20': { name: 'A20 - 20区粉尘环境保护级别', desc: '20区粉尘环境，平面结合面防爆结构，连续出现可燃性粉尘的场所', standard: 'GB/T 3836-2021' },
      'A21': { name: 'A21 - 21区粉尘环境保护级别', desc: '21区粉尘环境，平面结合面防爆结构，正常运行时可能出现可燃性粉尘的场所', standard: 'GB/T 3836-2021' },
      'A22': { name: 'A22 - 22区粉尘环境保护级别', desc: '22区粉尘环境，平面结合面防爆结构，异常情况下偶尔出现可燃性粉尘的场所', standard: 'GB/T 3836-2021' },
      'B20': { name: 'B20 - 20区粉尘环境保护级别', desc: '20区粉尘环境，直口结合面防爆结构，连续出现可燃性粉尘的场所', standard: 'GB/T 3836-2021' },
      'B21': { name: 'B21 - 21区粉尘环境保护级别', desc: '21区粉尘环境，直口结合面防爆结构，正常运行时可能出现可燃性粉尘的场所', standard: 'GB/T 3836-2021' },
      'B22': { name: 'B22 - 22区粉尘环境保护级别', desc: '22区粉尘环境，直口结合面防爆结构，异常情况下偶尔出现可燃性粉尘的场所', standard: 'GB/T 3836-2021' }
    };

    // 优先检查3字符的保护级别（如A21、B21）
    if (input.length >= 3) {
      const levelCode3 = input.substring(0, 3).toUpperCase();
      if (levels[levelCode3]) {
        return {
          found: true,
          result: {
            label: levels[levelCode3].name,
            value: levels[levelCode3].desc + ' (' + levels[levelCode3].standard + ')'
          },
          remaining: input.substring(3).trim()
        };
      }
    }

    // 检查2字符的保护级别（如Ga、Gb、Da、Db）
    if (input.length >= 2) {
      const levelCode2 = input.substring(0, 2).toUpperCase();
      if (levels[levelCode2]) {
        return {
          found: true,
          result: {
            label: levels[levelCode2].name,
            value: levels[levelCode2].desc + ' (' + levels[levelCode2].standard + ')'
          },
          remaining: input.substring(2).trim()
        };
      }
    }

    return { found: false };
  },

  // 解析IP防护等级
  parseIPRating(input) {
    // IP防护等级格式：IP + 两位数字
    const ipRegex = /^IP\s*(\d{2})/i;
    const match = input.match(ipRegex);

    if (match) {
      const ipCode = match[1];
      const fullMatch = match[0];

      // IP等级说明
      const ipDescriptions = {
        '65': 'IP65 - 防尘密封，防喷水',
        '66': 'IP66 - 防尘密封，防强烈喷水',
        '67': 'IP67 - 防尘密封，防短时浸水',
        '68': 'IP68 - 防尘密封，防持续浸水'
      };

      const description = ipDescriptions[ipCode] || `IP${ipCode} - 防护等级`;

      return {
        found: true,
        result: {
          label: `IP${ipCode} - 防护等级`,
          value: description
        },
        remaining: input.substring(fullMatch.length).trim()
      };
    }

    return { found: false };
  },

  // 选择示例
  selectExample(e) {
    const example = e.currentTarget.dataset.example;
    this.setData({
      inputValue: example
    });
    // 自动解析
    this.parseExplosionProof();
  },

  // 显示相机选择选项
  showCameraOptions() {
    wx.showActionSheet({
      itemList: ['拍照识别', '相册选择'],
      success: (res) => {
        if (res.tapIndex === 0) {
          // 拍照识别
          this.takePhoto();
        } else if (res.tapIndex === 1) {
          // 相册选择
          this.chooseImage();
        }
      },
      fail: (res) => {
        console.log('用户取消选择');
      }
    });
  },

  // 拍照识别
  takePhoto() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.handleImageSelected(tempFilePath);
      },
      fail: (err) => {
        console.error('拍照失败:', err);

        // 如果是权限问题，引导用户授权
        if (err.errMsg && err.errMsg.includes('auth')) {
          wx.showModal({
            title: '需要相机权限',
            content: '请允许访问相机以拍照识别防爆标识',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting();
              }
            }
          });
        } else {
          wx.showToast({
            title: '拍照失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 从相册选择图片
  chooseImage() {
    // 直接尝试选择图片，如果失败再处理权限问题
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.handleImageSelected(tempFilePath);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);

        // 如果是权限问题，引导用户授权
        if (err.errMsg && err.errMsg.includes('auth')) {
          wx.showModal({
            title: '需要相册权限',
            content: '请允许访问相册以选择图片识别防爆标识',
            confirmText: '去设置',
            cancelText: '取消',
            success: (modalRes) => {
              if (modalRes.confirm) {
                wx.openSetting({
                  success: (settingRes) => {
                    if (settingRes.authSetting['scope.album']) {
                      // 权限授权成功，重新尝试选择图片
                      this.chooseImage();
                    }
                  }
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: '选择图片失败',
            icon: 'none'
          });
        }
      }
    });
  },

  // 处理选中的图片
  handleImageSelected(imagePath) {
    // 将图片转换为base64格式以避免HTTP协议问题
    wx.getFileSystemManager().readFile({
      filePath: imagePath,
      encoding: 'base64',
      success: (res) => {
        const base64Image = 'data:image/jpeg;base64,' + res.data;
        this.setData({
          selectedImage: base64Image,
          originalImagePath: imagePath,
          showImageModal: true
        });
      },
      fail: (err) => {
        console.error('读取图片失败:', err);
        // 如果读取失败，仍然使用原路径
        this.setData({
          selectedImage: imagePath,
          originalImagePath: imagePath,
          showImageModal: true
        });
      }
    });
  },

  // 确认识别图片
  confirmRecognition() {
    const imagePath = this.data.originalImagePath || this.data.selectedImage;
    if (!imagePath) {
      wx.showToast({
        title: '请先选择图片',
        icon: 'none'
      });
      return;
    }

    this.setData({
      isRecognizing: true,
      showImageModal: false
    });

    wx.showLoading({
      title: '正在识别...'
    });

    // 如果有选择区域，先裁剪图片
    if (this.data.cropMode && this.data.hasCropSelection) {
      this.cropAndRecognizeImage(imagePath);
    } else {
      this.recognizeImage(imagePath);
    }
  },

  // 取消图片识别
  cancelRecognition() {
    this.setData({
      showImageModal: false,
      selectedImage: '',
      cropMode: false,
      hasCropSelection: false
    });
  },

  // 切换裁剪模式
  toggleCropMode() {
    const newCropMode = !this.data.cropMode;
    this.setData({
      cropMode: newCropMode,
      hasCropSelection: newCropMode
    });

    if (newCropMode) {
      // 进入裁剪模式时，重置选择框到图片中央
      this.resetCropBox();
    }
  },

  // 图片加载完成
  onImageLoad(e) {
    const { width, height } = e.detail;
    this.setData({
      originalImageWidth: width,
      originalImageHeight: height
    });

    // 获取图片显示区域的尺寸和位置
    this.calculateImageDisplayInfo();
  },

  // 计算图片显示信息
  calculateImageDisplayInfo() {
    const query = wx.createSelectorQuery();
    query.select('#previewImage').boundingClientRect();
    query.select('#imagePreview').boundingClientRect();
    query.exec((res) => {
      if (res[0] && res[1]) {
        const imageRect = res[0];
        const containerRect = res[1];

        this.setData({
          imageDisplayWidth: imageRect.width,
          imageDisplayHeight: imageRect.height,
          imageDisplayLeft: imageRect.left - containerRect.left,
          imageDisplayTop: imageRect.top - containerRect.top
        });

        // 如果是裁剪模式，重置选择框
        if (this.data.cropMode) {
          this.resetCropBox();
        }
      }
    });
  },

  // 重置裁剪框
  resetCropBox() {
    const { imageDisplayWidth, imageDisplayHeight } = this.data;
    if (imageDisplayWidth > 0 && imageDisplayHeight > 0) {
      const boxWidth = Math.min(200, imageDisplayWidth * 0.6);
      const boxHeight = Math.min(150, imageDisplayHeight * 0.6);

      this.setData({
        cropBox: {
          left: (imageDisplayWidth - boxWidth) / 2,
          top: (imageDisplayHeight - boxHeight) / 2,
          width: boxWidth,
          height: boxHeight
        }
      });
    }
  },

  // 裁剪框触摸开始
  onCropTouchStart(e) {
    if (!this.data.cropMode) return;

    const touch = e.touches[0];
    const handle = e.target.dataset.handle;

    this.setData({
      isDragging: true,
      dragHandle: handle || 'move',
      startTouch: {
        x: touch.clientX,
        y: touch.clientY
      },
      startCropBox: { ...this.data.cropBox }
    });
  },

  // 裁剪框触摸移动
  onCropTouchMove(e) {
    if (!this.data.isDragging || !this.data.cropMode) return;

    const touch = e.touches[0];
    const { startTouch, startCropBox, dragHandle, imageDisplayWidth, imageDisplayHeight } = this.data;

    const deltaX = touch.clientX - startTouch.x;
    const deltaY = touch.clientY - startTouch.y;

    let newCropBox = { ...startCropBox };

    // 根据拖拽的控制点调整裁剪框
    switch (dragHandle) {
      case 'move':
        // 移动整个框
        newCropBox.left = Math.max(0, Math.min(imageDisplayWidth - newCropBox.width, startCropBox.left + deltaX));
        newCropBox.top = Math.max(0, Math.min(imageDisplayHeight - newCropBox.height, startCropBox.top + deltaY));
        break;
      case 'tl':
        // 左上角
        newCropBox.left = Math.max(0, Math.min(startCropBox.left + startCropBox.width - 30, startCropBox.left + deltaX));
        newCropBox.top = Math.max(0, Math.min(startCropBox.top + startCropBox.height - 30, startCropBox.top + deltaY));
        newCropBox.width = startCropBox.width - (newCropBox.left - startCropBox.left);
        newCropBox.height = startCropBox.height - (newCropBox.top - startCropBox.top);
        break;
      case 'tr':
        // 右上角
        newCropBox.top = Math.max(0, Math.min(startCropBox.top + startCropBox.height - 30, startCropBox.top + deltaY));
        newCropBox.width = Math.max(30, Math.min(imageDisplayWidth - startCropBox.left, startCropBox.width + deltaX));
        newCropBox.height = startCropBox.height - (newCropBox.top - startCropBox.top);
        break;
      case 'bl':
        // 左下角
        newCropBox.left = Math.max(0, Math.min(startCropBox.left + startCropBox.width - 30, startCropBox.left + deltaX));
        newCropBox.width = startCropBox.width - (newCropBox.left - startCropBox.left);
        newCropBox.height = Math.max(30, Math.min(imageDisplayHeight - startCropBox.top, startCropBox.height + deltaY));
        break;
      case 'br':
        // 右下角
        newCropBox.width = Math.max(30, Math.min(imageDisplayWidth - startCropBox.left, startCropBox.width + deltaX));
        newCropBox.height = Math.max(30, Math.min(imageDisplayHeight - startCropBox.top, startCropBox.height + deltaY));
        break;
      case 't':
        // 上边
        newCropBox.top = Math.max(0, Math.min(startCropBox.top + startCropBox.height - 30, startCropBox.top + deltaY));
        newCropBox.height = startCropBox.height - (newCropBox.top - startCropBox.top);
        break;
      case 'r':
        // 右边
        newCropBox.width = Math.max(30, Math.min(imageDisplayWidth - startCropBox.left, startCropBox.width + deltaX));
        break;
      case 'b':
        // 下边
        newCropBox.height = Math.max(30, Math.min(imageDisplayHeight - startCropBox.top, startCropBox.height + deltaY));
        break;
      case 'l':
        // 左边
        newCropBox.left = Math.max(0, Math.min(startCropBox.left + startCropBox.width - 30, startCropBox.left + deltaX));
        newCropBox.width = startCropBox.width - (newCropBox.left - startCropBox.left);
        break;
    }

    this.setData({
      cropBox: newCropBox
    });
  },

  // 裁剪框触摸结束
  onCropTouchEnd() {
    this.setData({
      isDragging: false,
      dragHandle: ''
    });
  },

  // 裁剪图片并识别
  cropAndRecognizeImage(imagePath) {
    const { cropBox, imageDisplayWidth, imageDisplayHeight, originalImageWidth, originalImageHeight } = this.data;

    // 计算裁剪区域在原图中的位置和尺寸
    const scaleX = originalImageWidth / imageDisplayWidth;
    const scaleY = originalImageHeight / imageDisplayHeight;

    const cropInfo = {
      x: Math.round(cropBox.left * scaleX),
      y: Math.round(cropBox.top * scaleY),
      width: Math.round(cropBox.width * scaleX),
      height: Math.round(cropBox.height * scaleY)
    };

    // 直接传递裁剪信息给后端处理
    this.recognizeImage(imagePath, cropInfo);
  },

  // 图片OCR识别
  recognizeImage(imagePath, cropInfo = null) {
    // 先压缩图片以提高识别速度和减少流量
    this.compressImage(imagePath).then(compressedPath => {
      // 将图片转换为base64
      wx.getFileSystemManager().readFile({
        filePath: compressedPath,
        encoding: 'base64',
        success: (res) => {

          // 检查图片大小（base64编码后约为原图的4/3）
          if (res.data.length > 7 * 1024 * 1024) {
            wx.hideLoading();
            wx.showToast({
              title: '图片过大，请选择较小的图片',
              icon: 'none'
            });
            this.setData({
              isRecognizing: false
            });
            return;
          }

          // 准备请求数据
          const requestData = {
            image: res.data
          };

          // 如果有裁剪信息，添加到请求中
          if (cropInfo) {
            requestData.cropInfo = cropInfo;
          }

          // 调用PHP API进行OCR识别
          wx.request({
            url: 'https://sunxiyue.com/zdh/api/ocr_recognition.php',
            method: 'POST',
            header: {
              'content-type': 'application/json'
            },
            data: requestData,
            success: (result) => {
              wx.hideLoading();

              if (result.statusCode === 200) {
                if (result.data && typeof result.data === 'string' && result.data.trim() === '') {
                  console.error('OCR API返回空字符串');
                  wx.showModal({
                    title: '识别失败',
                    content: 'OCR服务返回空数据，可能的原因：\n1. 图片格式不支持\n2. 图片内容无法识别\n3. 服务器配置问题',
                    confirmText: '重试',
                    cancelText: '手动输入',
                    success: (res) => {
                      if (res.confirm) {
                        this.takePhoto();
                      }
                    }
                  });
                  this.setData({
                    isRecognizing: false
                  });
                  return;
                }

                // 尝试解析JSON响应
                let parsedData;
                try {
                  parsedData = typeof result.data === 'string' ? JSON.parse(result.data) : result.data;
                } catch (e) {
                  console.error('JSON解析失败:', e);
                  // 上报JSON解析错误
                  reportError('JSON_PARSE_ERROR', 'OCR响应JSON解析失败', {
                    originalError: e.message,
                    responseData: result.data,
                    statusCode: result.statusCode
                  });
                  wx.showToast({
                    title: 'OCR响应格式错误',
                    icon: 'none'
                  });
                  this.setData({
                    isRecognizing: false
                  });
                  return;
                }

                this.handleOCRResult(parsedData);
              } else {
                console.error('OCR API HTTP错误:', result.statusCode);
                // 上报HTTP错误
                reportError('HTTP_ERROR', `OCR API HTTP错误: ${result.statusCode}`, {
                  statusCode: result.statusCode,
                  responseData: result.data,
                  url: 'https://sunxiyue.com/zdh/api/ocr_recognition.php'
                });
                wx.showToast({
                  title: `OCR识别失败 (${result.statusCode})`,
                  icon: 'none'
                });
                this.setData({
                  isRecognizing: false
                });
              }
            },
            fail: (err) => {
              wx.hideLoading();
              console.error('OCR识别失败:', err);
              // 上报网络请求失败错误
              reportError('NETWORK_ERROR', 'OCR识别网络请求失败', {
                originalError: err.errMsg || err.message,
                url: 'https://sunxiyue.com/zdh/api/ocr_recognition.php'
              });
              wx.showToast({
                title: err.errMsg || 'OCR识别失败',
                icon: 'none'
              });
              this.setData({
                isRecognizing: false
              });
            }
          });
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('读取图片失败:', err);
          // 上报图片读取失败错误
          reportError('FILE_READ_ERROR', '读取图片文件失败', {
            originalError: err.errMsg || err.message
          });
          wx.showToast({
            title: '读取图片失败',
            icon: 'none'
          });
          this.setData({
            isRecognizing: false
          });
        }
      });
    }).catch(err => {
      wx.hideLoading();
      console.error('图片压缩失败:', err);
      wx.showToast({
        title: '图片处理失败',
        icon: 'none'
      });
      this.setData({
        isRecognizing: false
      });
    });
  },

  // 压缩图片
  compressImage(imagePath) {
    return new Promise((resolve, reject) => {
      wx.compressImage({
        src: imagePath,
        quality: 80, // 压缩质量
        success: (res) => {
          resolve(res.tempFilePath);
        },
        fail: (err) => {
          // 压缩失败时使用原图
          resolve(imagePath);
        }
      });
    });
  },

  // 处理OCR识别结果
  handleOCRResult(ocrResult) {
    try {
      this.setData({
        isRecognizing: false
      });

      if (ocrResult.code !== 0) {
        // 上报OCR识别失败错误
        reportError('OCR_RECOGNITION_FAILED', 'OCR识别返回错误', {
          ocrCode: ocrResult.code,
          ocrMessage: ocrResult.message
        });
        wx.showToast({
          title: ocrResult.message || 'OCR识别失败',
          icon: 'none',
          duration: 3000
        });
        return;
      }

    // 检查是否有识别结果
    if (!ocrResult.data || ocrResult.data.textCount === 0) {
      wx.showModal({
        title: '未识别到文字',
        content: '图片中没有识别到文字内容，请确保：\n1. 图片清晰\n2. 文字端正\n3. 光线充足\n4. 文字大小适中',
        confirmText: '重新拍照',
        cancelText: '手动输入',
        success: (res) => {
          if (res.confirm) {
            this.takePhoto();
          }
        }
      });
      return;
    }

    // 获取PHP API返回的防爆标识和所有文字
    let explosionProofCode = ocrResult.data.explosionProofCode;
    const allText = ocrResult.data.allText;

    // 如果服务器只返回了一个标识，尝试在客户端提取多个
    if (explosionProofCode && allText && !explosionProofCode.includes('/')) {
      const clientExtractedCodes = this.extractMultipleExplosionProofCodes(allText);
      if (clientExtractedCodes.length > 1) {
        explosionProofCode = clientExtractedCodes.join(' / ');
        console.log('客户端提取到多个防爆标识:', explosionProofCode);
      }
    }

    if (explosionProofCode) {
      this.setData({
        recognitionResult: explosionProofCode,
        inputValue: explosionProofCode
      });

      wx.showModal({
        title: '🎉 识别成功',
        content: `识别到防爆标识：\n${explosionProofCode}\n\n是否立即解析此标识？`,
        confirmText: '立即解析',
        cancelText: '手动修改',
        confirmColor: '#22c55e',
        success: (res) => {
          if (res.confirm) {
            // 延迟一下让用户看到结果
            setTimeout(() => {
              this.parseExplosionProof();
            }, 300);
          }
        }
      });
    } else {
      // 未识别到防爆标识，提示用户重新拍照或手动输入
      wx.showModal({
        title: '未识别到防爆标识',
        content: '图片中没有识别到有效的防爆标识，建议：\n1. 重新拍照，确保防爆标识清晰完整\n2. 调整光线和拍摄角度\n3. 手动输入防爆标识',
        confirmText: '重新拍照',
        cancelText: '手动输入',
        success: (res) => {
          if (res.confirm) {
            this.takePhoto();
          }
        }
      });
    }
    } catch (error) {
      // 捕获handleOCRResult函数中的任何JavaScript错误
      console.error('handleOCRResult函数执行错误:', error);
      reportError('JAVASCRIPT_ERROR', 'handleOCRResult函数执行错误', {
        originalError: error.message,
        stack: error.stack,
        ocrResult: ocrResult
      });

      this.setData({
        isRecognizing: false
      });

      wx.showToast({
        title: '处理识别结果时出错',
        icon: 'none'
      });
    }
  },



  // 切换参考标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab,
      currentReferenceData: this.data.referenceData[tab]
    });
  },

  // 显示详细介绍
  showDetail(e) {
    const item = e.currentTarget.dataset.item;
    if (item && item.detail) {
      this.setData({
        detailInfo: item.detail,
        showDetailModal: true
      });
    }
  },

  // 关闭详细介绍弹窗
  closeDetailModal() {
    this.setData({
      showDetailModal: false,
      detailInfo: {}
    });
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 客户端提取多个防爆标识
  extractMultipleExplosionProofCodes(text) {
    const foundCodes = [];

    // 防爆标识的正则表达式模式
    const patterns = [
      // 新标准粉尘格式：Ex tD A21 IP66 T95°C
      /Ex\s+tD\s+A\d{2}\s+IP\d{2}\s+T\d+°?C?/gi,
      /Ex\s+mD\s+A\d{2}\s+IP\d{2}\s+T\d+°?C?/gi,
      /Ex\s+tD\s+B\d{2}\s+IP\d{2}\s+T\d+°?C?/gi,
      /Ex\s+mD\s+B\d{2}\s+IP\d{2}\s+T\d+°?C?/gi,

      // 旧标准格式：Ex d IIB T5 Gb
      /Ex\s+[a-z]+\s+I{1,3}[ABC]?\s+T\d+\s+[GDM][abc]/gi,

      // 新标准气体格式：Ex db IIB T6 Gb
      /Ex\s+[a-z]{2,4}\s+I{1,3}[ABC]?\s+T\d+°?C?\s+[GDM][abc]/gi,

      // 更宽松的格式
      /Ex\s+[a-z]{1,4}\s+I{1,3}[ABC]?\s+T\d+/gi,
      /Ex\s+[a-z]{2,4}\s+[AB]\d{2}\s+IP\d{2}\s+T\d+°?C?/gi
    ];

    // 对每种模式进行匹配
    patterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const cleaned = this.cleanExplosionProofCode(match);
          if (cleaned && !foundCodes.includes(cleaned)) {
            foundCodes.push(cleaned);
          }
        });
      }
    });

    return foundCodes;
  },

  // 清理防爆标识代码
  cleanExplosionProofCode(code) {
    if (!code) return null;

    // 移除多余空格
    code = code.replace(/\s+/g, ' ').trim();

    // 修正大小写
    code = code.replace(/Ex\s+([a-z]+)/gi, (match, type) => {
      if (type.toLowerCase() === 'td') return 'Ex tD';
      if (type.toLowerCase() === 'md') return 'Ex mD';
      return 'Ex ' + type.toLowerCase();
    });

    // 修正设备类别大小写
    code = code.replace(/(I{1,3}[ABC]?)/gi, (match) => match.toUpperCase());

    // 修正温度格式
    code = code.replace(/T(\d+)°?C?/gi, 'T$1°C');

    // 对于旧标准格式，去掉°C
    if (/Ex\s+[a-z]+\s+I{1,3}[ABC]?\s+T\d+°C\s+[GDM][abc]/i.test(code)) {
      code = code.replace(/T(\d+)°C/gi, 'T$1');
    }

    // 修正EPL等级大小写
    code = code.replace(/\b([GDM][abc])\b/gi, (match) => {
      return match.charAt(0).toUpperCase() + match.charAt(1).toLowerCase();
    });

    // 修正区域级别格式
    code = code.replace(/\b([AB])(\d{2})\b/gi, (match, letter, number) => {
      return letter.toUpperCase() + number;
    });

    // 修正IP防护等级格式
    code = code.replace(/IP\s*(\d+)/gi, 'IP$1');

    return code;
  },

  // 转发给朋友
  onShareAppMessage() {
    return {
      title: '防爆标识查询工具',
      path: '/pages/explosionProofQuery/explosionProofQuery'
    };
  },

  // 分享到朋友圈
  onShareTimeline() {
    return {
      title: '防爆标识查询工具',
      query: 'from=timeline'
    };
  }
});
