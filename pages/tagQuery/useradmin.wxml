<view class="container">
  <view class="header">
    <view class="title">用户管理</view>
    <view class="add-btn" bindtap="showAddUser">添加用户</view>
  </view>
  
  <view class="search-box">
    <input type="text" class="search-input" placeholder="搜索用户名" value="{{keyword}}" bindinput="searchUser" />
  </view>
  
  <!-- 用户列表标题 -->
  <view class="list-header">
    <view class="col-username">用户名</view>
    <view class="col-role">角色</view>
    <view class="col-actions">操作</view>
  </view>
  
  <!-- 用户列表数据 -->
  <view class="user-list">
    <block wx:if="{{userList.length > 0}}">
      <view class="user-item" wx:for="{{userList}}" wx:key="id">
        <!-- 用户名 -->
        <view class="col-username">{{item.username}}</view>
        
        <!-- 角色 -->
        <view class="col-role">
          <view wx:if="{{item.role == 'admin'}}" class="role-tag admin">超级管理员</view>
          <view wx:elif="{{item.role == 'manager'}}" class="role-tag manager">普通管理员</view>
          <view wx:else class="role-tag user">普通用户</view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="col-actions">
          <text class="action-btn" bindtap="showActionSheet" data-id="{{item.id}}" data-username="{{item.username}}" data-role="{{item.role}}">操作</text>
        </view>
      </view>
    </block>
    
    <!-- 空列表提示 -->
    <view class="empty-state" wx:if="{{!isLoading && userList.length === 0}}">
      <view class="empty-icon">🔍</view>
      <view class="empty-text">没有找到用户</view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading" wx:if="{{isLoading}}">
      <view class="loading-text">加载中...</view>
    </view>
  </view>
  
  <!-- 添加用户弹窗 -->
  <view class="modal" wx:if="{{showAddModal}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">添加用户</text>
        <text class="modal-close" bindtap="hideAddModal">×</text>
      </view>
      <view class="modal-body">
        <view class="form-item">
          <text class="form-label">用户名：</text>
          <input class="form-input" placeholder="请输入用户名" value="{{newUsername}}" bindinput="inputUsername" />
        </view>
        <view class="form-item">
          <text class="form-label">密码：</text>
          <input class="form-input" placeholder="请输入密码" password="true" value="{{newPassword}}" bindinput="inputPassword" />
        </view>
        <view class="form-item">
          <text class="form-label">角色：</text>
          <radio-group bindchange="selectRole" class="role-radio-group">
            <label class="role-radio">
              <radio value="user" checked="{{newRole === 'user'}}" />普通用户
            </label>
            <label class="role-radio">
              <radio value="manager" checked="{{newRole === 'manager'}}" />普通管理员
            </label>
            <label class="role-radio">
              <radio value="admin" checked="{{newRole === 'admin'}}" />超级管理员
            </label>
          </radio-group>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn btn-cancel" bindtap="hideAddModal">取消</button>
        <button class="btn btn-confirm" bindtap="addUser">确定</button>
      </view>
    </view>
  </view>

  <!-- 角色编辑弹窗 (超简化版) -->
  <view class="modal" wx:if="{{roleModalVisible}}">
    <view class="modal-content">
      <view class="modal-header">
        <text class="modal-title">修改角色</text>
        <text class="modal-close" bindtap="hideRoleModal">×</text>
      </view>
      <view class="modal-body">
        <view class="user-info">用户: {{currentUser.username}}</view>
        <radio-group class="simple-radio-group" bindchange="selectRole">
          <label class="simple-radio">
            <radio value="user" checked="{{selectedRole === 'user'}}"/>
            <view class="simple-radio-text user">普通用户</view>
          </label>
          <label class="simple-radio">
            <radio value="manager" checked="{{selectedRole === 'manager'}}"/>
            <view class="simple-radio-text manager">普通管理员</view>
          </label>
          <label class="simple-radio">
            <radio value="admin" checked="{{selectedRole === 'admin'}}"/>
            <view class="simple-radio-text admin">超级管理员</view>
          </label>
        </radio-group>
      </view>
      <view class="modal-footer">
        <button class="btn btn-cancel" hover-class="navigator-hover" bindtap="hideRoleModal">取消</button>
        <button class="btn btn-confirm" hover-class="navigator-hover" bindtap="confirmRoleChange">确定</button>
      </view>
    </view>
  </view>
</view> 