Page({
  data: {
    userList: [],
    isLoading: false,
    keyword: '',
    showAddModal: false,
    newUsername: '',
    newPassword: '',
    newRole: 'user',
    roles: [
      { value: 'admin', label: '管理员' },
      { value: 'manager', label: '普通管理员' },
      { value: 'user', label: '普通用户' }
    ],
    // 操作相关
    currentUser: null,
    showActionModal: false,
    roleModalVisible: false,
    selectedRole: 'user'
  },

  onLoad: function () {
    this.fetchUserList();
  },

  onShow: function () {
    // 验证用户是否有权限访问
    const token = wx.getStorageSync('userToken');
    const role = wx.getStorageSync('userRole');
    
    // 只允许admin角色访问
    if (role !== 'admin') {
      wx.showModal({
        title: '无权访问',
        content: '只有系统管理员才能访问用户管理功能',
        showCancel: false,
        success: () => {
          wx.navigateBack();
        }
      });
      return;
    }
    
    this.fetchUserList();
  },

  // 获取角色对应的样式类
  getRoleClass: function(role) {
    switch(role) {
      case 'admin':
        return 'admin';
      case 'manager':
        return 'manager';
      case 'user':
      default:
        return 'user';
    }
  },

  // 获取角色名称
  getRoleName: function(role) {
    switch(role) {
      case 'admin':
        return '超级管理员';
      case 'manager':
        return '普通管理员';
      case 'user':
      default:
        return '普通用户';
    }
  },

  // 获取用户列表
  fetchUserList: function () {
    const token = wx.getStorageSync('userToken');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      return;
    }

    this.setData({ isLoading: true });

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/api.php',
      method: 'POST',
      data: {
        action: 'get_users',
        token: token,
        keyword: this.data.keyword
      },
      success: (res) => {
        if (res.data.status === 'success') {
          this.setData({
            userList: res.data.data || []
          });
        } else {
          wx.showToast({
            title: res.data.message || '获取用户列表失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 搜索用户
  searchUser: function (e) {
    this.setData({
      keyword: e.detail.value
    });
    this.fetchUserList();
  },

  // 显示添加用户弹窗
  showAddUser: function () {
    this.setData({
      showAddModal: true,
      newUsername: '',
      newPassword: '',
      newRole: 'user'
    });
  },

  // 隐藏添加用户弹窗
  hideAddModal: function () {
    this.setData({
      showAddModal: false
    });
  },

  // 输入新用户名
  inputNewUsername: function (e) {
    this.setData({
      newUsername: e.detail.value
    });
  },

  // 输入新密码
  inputNewPassword: function (e) {
    this.setData({
      newPassword: e.detail.value
    });
  },

  // 输入用户名别名
  inputUsername: function(e) {
    this.inputNewUsername(e);
  },

  // 输入密码别名
  inputPassword: function(e) {
    this.inputNewPassword(e);
  },

  // 选择角色
  selectRole: function (e) {
    if (e.detail && typeof e.detail.value === 'string') {
      // 从radio-group选择
      const role = e.detail.value;
      this.setData({
        selectedRole: role
      });
    } else if (e.currentTarget && e.currentTarget.dataset.role) {
      // 从角色选项点击
      const role = e.currentTarget.dataset.role;
      this.setData({
        selectedRole: role
      });
    }
  },

  // 打开角色编辑弹窗
  openRoleModal: function(e) {
    const userId = e.currentTarget.dataset.id;
    const username = e.currentTarget.dataset.username;
    const currentRole = e.currentTarget.dataset.role;

    this.setData({
      roleModalVisible: true,
      currentUser: {
        id: userId,
        username: username,
        role: currentRole
      },
      selectedRole: currentRole
    });
  },

  // 隐藏角色编辑弹窗
  hideRoleModal: function() {
    this.setData({
      roleModalVisible: false
    });
  },

  // 确认更改角色
  confirmRoleChange: function() {
    const that = this;
    const userId = this.data.currentUser.id;
    const role = this.data.selectedRole;
    
    if (!userId || !role) {
      wx.showToast({
        title: '数据错误',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '正在修改...'
    });

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/api.php',
      method: 'POST',
      data: {
        action: 'change_role',
        token: wx.getStorageSync('userToken'),
        user_id: userId,
        role: role
      },
      success: function(res) {
        wx.hideLoading();
        
        if (res.data.status === 'success') {
          wx.showToast({
            title: '角色已更新',
            icon: 'success'
          });

          // 更新本地列表
          const users = that.data.userList.map(user => {
            if (user.id === userId) {
              user.role = role;
            }
            return user;
          });

          that.setData({
            userList: users,
            roleModalVisible: false
          });
        } else {
          wx.showToast({
            title: res.data.message || '操作失败',
            icon: 'none'
          });
        }
      },
      fail: function(err) {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 添加用户
  addUser: function () {
    const that = this;
    const username = this.data.newUsername.trim();
    const password = this.data.newPassword.trim();
    const role = this.data.newRole;

    if (username.length < 2) {
      wx.showToast({
        title: '用户名长度不能少于2位',
        icon: 'none'
      });
      return;
    }

    if (password.length < 6) {
      wx.showToast({
        title: '密码长度不能少于6位',
        icon: 'none'
      });
      return;
    }

    wx.request({
      url: 'https://sunxiyue.com/zdh/api/api.php',
      method: 'POST',
      data: {
        action: 'add_user',
        token: wx.getStorageSync('userToken'),
        username: username,
        password: password,
        role: role
      },
      success: function(res) {
        if (res.data.status === 'success') {
          wx.showToast({
            title: '用户添加成功',
            icon: 'success'
          });
          that.setData({
            showAddModal: false,
            newUsername: '',
            newPassword: '',
            newRole: 'user'
          });
          // 刷新用户列表
          that.fetchUserList();
        } else {
          wx.showToast({
            title: res.data.message || '添加失败',
            icon: 'none'
          });
        }
      },
      fail: function() {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    });
  },

  // 显示用户操作菜单
  showActionSheet: function (e) {
    const userId = e.currentTarget.dataset.id;
    const username = e.currentTarget.dataset.username;
    const role = e.currentTarget.dataset.role;
    
    const user = {
      id: userId,
      username: username,
      role: role
    };
    
    this.setData({
      currentUser: user
    });
    
    wx.showActionSheet({
      itemList: ['重置密码', '修改角色', '删除用户'],
      success: (res) => {
        if (res.tapIndex === 0) {
          this.resetPassword();
        } else if (res.tapIndex === 1) {
          this.openRoleModal({
            currentTarget: {
              dataset: {
                id: userId,
                username: username,
                role: role
              }
            }
          });
        } else if (res.tapIndex === 2) {
          this.deleteUser();
        }
      }
    });
  },

  // 重置密码
  resetPassword: function () {
    const user = this.data.currentUser;
    
    wx.showModal({
      title: '重置密码',
      content: `确定要重置用户 ${user.username} 的密码吗？新密码将设为: 000000`,
      success: (res) => {
        if (res.confirm) {
          const token = wx.getStorageSync('userToken');
          
          wx.showLoading({
            title: '重置中...'
          });
          
          wx.request({
            url: 'https://sunxiyue.com/zdh/api/api.php',
            method: 'POST',
            data: {
              action: 'reset_password',
              token: token,
              user_id: user.id
            },
            success: (res) => {
              wx.hideLoading();
              if (res.data.status === 'success') {
                wx.showToast({
                  title: '密码已重置',
                  icon: 'success'
                });
              } else {
                wx.showToast({
                  title: res.data.message || '重置失败',
                  icon: 'none'
                });
              }
            },
            fail: () => {
              wx.hideLoading();
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  },

  // 删除用户
  deleteUser: function () {
    const user = this.data.currentUser;
    
    wx.showModal({
      title: '删除用户',
      content: `确定要删除用户 ${user.username} 吗？此操作不可恢复！`,
      success: (res) => {
        if (res.confirm) {
          const token = wx.getStorageSync('userToken');
          
          wx.showLoading({
            title: '删除中...'
          });
          
          wx.request({
            url: 'https://sunxiyue.com/zdh/api/api.php',
            method: 'POST',
            data: {
              action: 'delete_user',
              token: token,
              user_id: user.id
            },
            success: (res) => {
              wx.hideLoading();
              if (res.data.status === 'success') {
                wx.showToast({
                  title: '用户已删除',
                  icon: 'success'
                });
                this.fetchUserList();
              } else {
                wx.showToast({
                  title: res.data.message || '删除失败',
                  icon: 'none'
                });
              }
            },
            fail: () => {
              wx.hideLoading();
              wx.showToast({
                title: '网络错误，请重试',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  }
}); 