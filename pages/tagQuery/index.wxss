/* pages/tagQuery/index.wxss */

.container {
  position: relative;
  min-height: 100vh;
  background-color: #f5f6fa;
}

/* 用户信息容器 */
.user-info-container {
  background: #f8f9fa;
  padding: 20rpx 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
  position: sticky;
  top: 0;
  z-index: 999;
}

/* 用户信息栏 */
.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
}

.user-name {
  font-size: 28rpx;
  color: #2c3e50;
  max-width: 200rpx;
}

.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  margin-left: 15rpx;
}

.password-btn {
  background: #ecf0f1;
  color: #34495e;
}

.admin-btn {
  background: #e8f4f8;
  color: #3498db;
}

.logout-btn {
  background: #fdedec;
  color: #e74c3c;
}

/* 主界面容器 */
.main-container {
  padding: 20rpx;
  background-color: #f5f6fa;
  min-height: 100vh;
}

/* 主选项卡样式 */
.main-tabs {
  display: flex;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.main-tab {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 30rpx;
  color: #7f8c8d;
  position: relative;
}

.main-tab.active {
  color: #3498db;
  font-weight: bold;
}

.main-tab.active:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 6rpx;
  background-color: #3498db;
  border-radius: 3rpx;
}

.tab-content {
  margin-top: 20rpx;
}

/* 查询区域 */
.query-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.query-type {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.type-item {
  flex: 1;
  text-align: center;
  padding: 15rpx 0;
  font-size: 28rpx;
  color: #7f8c8d;
  border-bottom: 4rpx solid transparent;
}

.type-item.active {
  color: #3498db;
  border-bottom: 4rpx solid #3498db;
  font-weight: bold;
}

.query-form {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.query-input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 180rpx 0 20rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.input-container {
  width: 100%;
  position: relative;
}

.inline-tip {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #95a5a6;
  background-color: #f5f5f5;
  padding: 4rpx 10rpx;
  border-radius: 6rpx;
  text-align: center;
}

.query-btn {
  width: 100%;
  height: 80rpx;
  background: #3498db;
  color: white;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8rpx;
  padding: 0;
  line-height: 80rpx;
  margin: 0;
}

/* 结果区域 */
.result-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
}

.result-title {
  font-size: 28rpx;
  color: #333;
  padding: 20rpx 0 10rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eaeaea;
  margin-bottom: 15rpx;
}

.result-header {
  margin-bottom: 20rpx;
}

.header-item {
  font-size: 28rpx;
  color: #34495e;
  font-weight: 500;
  display: block;
  margin-bottom: 10rpx;
}

.section-title {
  font-size: 28rpx;
  color: #7f8c8d;
  margin: 20rpx 0 15rpx;
}

.result-list {
  margin-bottom: 30rpx;
}

.list-item {
  background-color: #ffffff;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item.clickable {
  position: relative;
  padding-right: 40rpx;
}

.item-arrow {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 36rpx;
  color: #bdc3c7;
}

.clickable {
  position: relative;
}

.clickable:active {
  background-color: #f5f5f5;
}

.header-item.clickable {
  color: #3498db;
  text-decoration: underline;
}

.detail-item.clickable {
  color: #3498db;
}

.item-code {
  font-size: 32rpx;
  font-weight: bold;
  color: #0366d6;
}

.item-name {
  font-size: 30rpx;
  color: #444;
  flex: 1;
  margin-left: 16rpx;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-remark {
  font-size: 24rpx;
  color: #95a5a6;
  margin-left: 20rpx;
}

.tag-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background-color: #e6f4ff;
  border-bottom: 1rpx solid #e8e8e8;
}

.tag-detail {
  padding: 12rpx 16rpx;
  background-color: #f9f9f9;
  border-radius: 0 0 8rpx 8rpx;
  display: flex;
  flex-wrap: wrap;
}

.detail-item {
  font-size: 26rpx;
  line-height: 42rpx;
  color: #333;
  padding: 6rpx 12rpx;
  margin: 4rpx 8rpx;
  width: 45%;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
}

.detail-item.full-width {
  width: 100%;
  background-color: #f0f8ff;
  border-radius: 4rpx;
  padding: 10rpx 16rpx;
  margin: 6rpx 0;
}

/* 为不同类型的信息添加视觉区分 */
.detail-item:nth-child(odd) {
  background-color: #f5f5f5;
}

.detail-item:nth-child(even) {
  background-color: #fafafa;
}

.no-data, .no-result {
  text-align: center;
  color: #95a5a6;
  font-size: 28rpx;
  padding: 40rpx 0;
}

.result-item {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx dashed #e0e0e0;
}

.result-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

/* 返回顶部按钮 */
.back-to-top {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  padding: 20rpx 30rpx;
  background: rgba(52, 152, 219, 0.9);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.15);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.back-to-top.show {
  opacity: 1;
  visibility: visible;
}

.back-to-top-text {
  color: white;
  font-size: 28rpx;
  font-weight: bold;
}

.back-to-top:active {
  transform: scale(0.95);
  background: rgba(52, 152, 219, 1);
}

/* 搜索提示样式 */
.search-tip {
  font-size: 24rpx;
  color: #666;
  margin-top: 8rpx;
  margin-bottom: 12rpx;
  padding-left: 10rpx;
}

/* 登录界面样式 */
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f6fa;
  z-index: 999;
}

.login-box {
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 20rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.login-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 50rpx;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.login-input {
  width: 100%;
  height: 90rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 0 30rpx;
  font-size: 30rpx;
  border: 2rpx solid #e9ecef;
  box-sizing: border-box;
}

.login-btn {
  width: 100%;
  height: 90rpx;
  background: #3498db;
  color: white;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30rpx;
}

.login-btn:active {
  opacity: 0.9;
}

/* 记住功能样式 */
.remember-options {
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin: 20rpx 0;
  padding: 0 20rpx;
}

.remember-item {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.remember-checkbox {
  margin-right: 12rpx;
  transform: scale(0.9);
}

.remember-text {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  user-select: none;
}

.login-notice {
  margin-top: 40rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);
}

.notice-title {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: bold;
  margin-bottom: 15rpx;
  text-align: center;
}

.notice-item {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-bottom: 10rpx;
  line-height: 1.5;
}

/* 弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
}

.modal-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  z-index: 1001;
  box-sizing: border-box;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-content {
  padding: 30rpx;
  box-sizing: border-box;
}

.modal-input {
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  font-size: 30rpx;
}

.cancel {
  color: #999;
  border-right: 1rpx solid #f0f0f0;
}

.confirm {
  color: #3498db;
  font-weight: bold;
}

.password-tip {
  font-size: 24rpx;
  color: #888;
  margin-top: -10rpx;
  margin-bottom: 10rpx;
  padding-left: 10rpx;
}

/* 阀门查询样式 */
.valve-result-item {
  border-bottom: 2rpx solid #f0f0f0;
  padding: 20rpx 0;
}

.valve-result-item:last-child {
  border-bottom: none;
}

.valve-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.valve-detail {
  display: flex;
  flex-wrap: wrap;
}

.valve-detail-item {
  font-size: 26rpx;
  color: #7f8c8d;
  margin-right: 20rpx;
  margin-bottom: 10rpx;
}

/* 阀门信息样式 */
.valve-info-container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.valve-number, .valve-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6rpx 0;
}

.valve-number {
  font-size: 30rpx;
  font-weight: bold;
  color: #0366d6;
  margin-bottom: 8rpx;
}

.valve-name {
  font-size: 28rpx;
  color: #444;
}

.copy-btn {
  background-color: #f0f0f0;
  color: #666;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-left: 16rpx;
}

.copy-btn:active {
  background-color: #e0e0e0;
}

/* 复制按钮组 */
.button-group {
  display: flex;
  align-items: center;
}

.copy-all-btn {
  background-color: #e6f4ff;
  color: #0366d6;
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
  margin-left: 10rpx;
  font-weight: 500;
}

.copy-btn:active, .copy-all-btn:active {
  opacity: 0.8;
}

/* 复制全部结果按钮 */
.copy-all-results-btn {
  background-color: #e6f7ff;
  color: #1890ff;
  font-size: 26rpx;
  padding: 8rpx 16rpx;
  border-radius: 6rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
  font-weight: 500;
}

.copy-all-results-btn:active {
  opacity: 0.8;
}

/* 添加加载状态样式 */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 999;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 