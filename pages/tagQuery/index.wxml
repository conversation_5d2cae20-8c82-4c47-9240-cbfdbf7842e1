<!-- pages/tagQuery/index.wxml -->
<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{isLoading}}">
    <view class="loading-spinner"></view>
  </view>

  <!-- 用户信息栏，登录后才显示 -->
  <view class="user-info-container" wx:if="{{isLoggedIn && !isLoading}}">
    <view class="user-info">
      <view class="user-name text-ellipsis">欢迎，{{userInfo.username}}</view>
      <view class="user-actions">
        <view class="action-btn password-btn" bindtap="showChangePassword">修改密码</view>
        <view class="action-btn admin-btn" wx:if="{{userRole === 'admin'}}" bindtap="goToUserAdmin">用户管理</view>
        <view class="action-btn logout-btn" bindtap="handleLogout">退出登录</view>
      </view>
    </view>
  </view>

  <!-- 登录界面 -->
  <view class="login-container" wx:if="{{!isLoggedIn && !isLoading}}">
    <view class="login-box">
      <view class="login-title">登录仪表位号及阀门编号查询</view>
      <view class="login-form">
        <input class="login-input" type="text" placeholder="请输入用户名" model:value="{{username}}" bindinput="onInputUsername" />
        <input class="login-input" type="password" placeholder="请输入密码" model:value="{{password}}" bindinput="onInputPassword" password="true" />

        <!-- 记住功能选项 -->
        <view class="remember-options">
          <view class="remember-item" bindtap="toggleRememberUsername">
            <checkbox
              class="remember-checkbox"
              value="username"
              checked="{{rememberUsername}}"
            />
            <text class="remember-text">记住账号</text>
          </view>
          <view class="remember-item" bindtap="toggleRememberPassword">
            <checkbox
              class="remember-checkbox"
              value="password"
              checked="{{rememberPassword}}"
            />
            <text class="remember-text">记住密码</text>
          </view>
        </view>

        <button class="login-btn" bindtap="handleLogin">登录</button>
      </view>
      <view class="login-notice">
        <view class="notice-title">温馨提示</view>
        <view class="notice-item">• 需要登录后才能使用查询功能</view>
        <view class="notice-item">• 如需账号，请联系系统管理员申请</view>
        <view class="notice-item">• 已有账号的用户请直接登录</view>
      </view>
    </view>
  </view>

  <!-- 主界面 -->
  <view class="main-container" wx:if="{{isLoggedIn && !isLoading}}">
    <!-- 主查询类型切换 -->
    <view class="main-tabs">
      <view class="main-tab {{activeTab === 'instrument' ? 'active' : ''}}" bindtap="switchMainTab" data-tab="instrument">仪表位号查询</view>
      <view class="main-tab {{activeTab === 'valve' ? 'active' : ''}}" bindtap="switchMainTab" data-tab="valve">阀门编号查询</view>
    </view>

    <!-- 仪表位号查询内容 -->
    <view class="tab-content" wx:if="{{activeTab === 'instrument'}}">
      <!-- 查询区域 -->
      <view class="query-section">
        <!-- 查询类型选择 -->
        <view class="query-type">
          <view class="type-item {{queryType === 'rcp' ? 'active' : ''}}" bindtap="switchQueryType" data-type="rcp">RCP柜查询</view>
          <view class="type-item {{queryType === 'junction' ? 'active' : ''}}" bindtap="switchQueryType" data-type="junction">接线箱查询</view>
          <view class="type-item {{queryType === 'tag' ? 'active' : ''}}" bindtap="switchQueryType" data-type="tag">位号查询</view>
        </view>

        <!-- RCP柜查询 -->
        <view class="query-form" wx:if="{{queryType === 'rcp'}}">
          <input class="query-input" placeholder="请输入RCP柜名称" bindinput="inputRcpName" value="{{rcpName}}"/>
          <button class="query-btn" bindtap="queryRcp">查询</button>
        </view>

        <!-- 接线箱查询 -->
        <view class="query-form" wx:if="{{queryType === 'junction'}}">
          <input class="query-input" placeholder="请输入接线箱名称" bindinput="inputJunctionName" value="{{junctionName}}"/>
          <button class="query-btn" bindtap="queryJunction">查询</button>
        </view>

        <!-- 位号查询 -->
        <view class="query-form" wx:if="{{queryType === 'tag'}}">
          <view class="input-container">
            <input 
              class="query-input" 
              placeholder="请输入位号或名称" 
              bindinput="inputTagInfo" 
              value="{{tagInfo}}"
            />
            <view class="inline-tip">空格分隔多关键词</view>
          </view>
          <button class="query-btn" bindtap="queryTag">查询</button>
        </view>
      </view>

      <!-- 结果区域 -->
      <view class="result-section">
        <!-- 查询结果标题 -->
        <view class="result-title" wx:if="{{showResult}}">
          <text>查询结果 (共{{resultCount}}项)</text>
        </view>

        <!-- RCP柜查询结果 -->
        <view class="result-content" wx:if="{{queryType === 'rcp' && showResult}}">
          <block wx:for="{{rcpResults}}" wx:key="id">
            <view class="result-item">
              <view class="result-header">
                <text class="header-item">RCP柜: {{item.name}}</text>
                <text class="header-item">所属电源箱: {{item.power_name}}</text>
                <text class="header-item">位置: {{item.location || '未知'}}</text>
              </view>
              
              <!-- 接线箱列表 -->
              <view class="section-title">接线箱列表</view>
              <view class="result-list" wx:if="{{item.junctionBoxes && item.junctionBoxes.length > 0}}">
                <view class="list-item clickable" wx:for="{{item.junctionBoxes}}" wx:for-item="junction" wx:key="id" bindtap="navigateToJunction" data-name="{{junction.name}}">
                  <text class="item-name">{{junction.name}}</text>
                  <text class="item-remark">{{junction.remark || ''}}</text>
                  <view class="item-arrow">›</view>
                </view>
              </view>
              <view class="no-data" wx:else>无接线箱数据</view>
              
              <!-- 位号列表 -->
              <view class="section-title">位号列表</view>
              <view class="result-list" wx:if="{{item.terminals && item.terminals.length > 0}}">
                <view class="list-item clickable" wx:for="{{item.terminals}}" wx:for-item="terminal" wx:key="id" bindtap="navigateToTag" data-code="{{terminal.code}}">
                  <text class="item-code">{{terminal.code}}</text>
                  <text class="item-name">{{terminal.cable_name || ''}}</text>
                  <text class="item-remark">{{terminal.remark || ''}}</text>
                  <view class="item-arrow">›</view>
                </view>
              </view>
              <view class="no-data" wx:else>无位号数据</view>
            </view>
          </block>
        </view>

        <!-- 接线箱查询结果 -->
        <view class="result-content" wx:if="{{queryType === 'junction' && showResult}}">
          <block wx:for="{{junctionResults}}" wx:key="id">
            <view class="result-item">
              <view class="result-header">
                <text class="header-item">接线箱: {{item.name}}</text>
                <text class="header-item clickable" bindtap="navigateToRcp" data-name="{{item.rcp_name}}">所属RCP柜: {{item.rcp_name}} ›</text>
              </view>
              
              <!-- 位号列表 -->
              <view class="section-title">位号列表</view>
              <view class="result-list" wx:if="{{item.terminals && item.terminals.length > 0}}">
                <view class="list-item clickable" wx:for="{{item.terminals}}" wx:for-item="terminal" wx:key="id" bindtap="navigateToTag" data-code="{{terminal.code}}">
                  <text class="item-code">{{terminal.code}}</text>
                  <text class="item-name">{{terminal.cable_name || ''}}</text>
                  <text class="item-remark">{{terminal.remark || ''}}</text>
                  <view class="item-arrow">›</view>
                </view>
              </view>
              <view class="no-data" wx:else>无位号数据</view>
            </view>
          </block>
        </view>

        <!-- 位号查询结果 -->
        <view class="result-content" wx:if="{{queryType === 'tag' && showResult}}">
          <view class="result-list" wx:if="{{tagResults && tagResults.length > 0}}">
            <view class="list-item" wx:for="{{tagResults}}" wx:key="id">
              <view class="tag-header">
                <text class="item-code">{{item.code}}</text>
                <text class="item-name">{{item.cable_name || ''}}</text>
              </view>
              <view class="tag-detail">
                <text class="detail-item clickable" bindtap="navigateToRcp" data-name="{{item.rcp_name}}">RCP柜: {{item.rcp_name}} ›</text>
                <text class="detail-item clickable" wx:if="{{item.junction_name}}" bindtap="navigateToJunction" data-name="{{item.junction_name}}">接线箱: {{item.junction_name}} ›</text>
                <text class="detail-item" wx:if="{{item.remark}}">备注: {{item.remark}}</text>
              </view>
            </view>
          </view>
          <view class="no-data" wx:else>未找到匹配的位号</view>
        </view>

        <!-- 无结果提示 -->
        <view class="no-result" wx:if="{{noResult}}">
          <text>未找到匹配的数据</text>
          <view class="contact-admin">
            <text>如果您确认信息正确但无法查询，请联系管理员</text>
            <text class="contact-info">联系方式：管理员微信或电话</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 阀门号查询内容 -->
    <view class="tab-content" wx:if="{{activeTab === 'valve'}}">
      <!-- 查询区域 -->
      <view class="query-section">
        <view class="query-form">
          <view class="input-container">
            <input 
              class="query-input" 
              placeholder="请输入阀门号或名称" 
              bindinput="inputValveInfo" 
              value="{{valveInfo}}"
            />
            <view class="inline-tip">空格分隔多关键词</view>
          </view>
          <button class="query-btn" bindtap="queryValve">查询</button>
        </view>
      </view>

      <!-- 结果区域 -->
      <view class="result-section">
        <!-- 查询结果标题 -->
        <view class="result-title" wx:if="{{showValveResult}}">
          <text>查询结果 (共{{valveResultCount}}项)</text>
          <view class="copy-all-results-btn" bindtap="copyAllValveResults">复制全部结果</view>
        </view>

        <!-- 阀门查询结果 -->
        <view class="result-content" wx:if="{{showValveResult}}">
          <view class="result-list" wx:if="{{valveResults && valveResults.length > 0}}">
            <view class="list-item" wx:for="{{valveResults}}" wx:key="id">
              <view class="tag-header">
                <view class="valve-info-container">
                  <view class="valve-number">
                    <text>阀门编号：{{item.valve_no}}</text>
                    <view class="copy-all-btn" bindtap="copyAllValveInfo" data-valve="{{index}}">复制阀门信息</view>
                  </view>
                  <view class="valve-name">
                    <text>阀门名称：{{item.name}}</text>
                  </view>
                </view>
              </view>
              <view class="tag-detail">
                <text class="detail-item">平台名称: {{item.platform_name || '未知'}}</text>
                <text class="detail-item">位置: {{item.location || '未知'}}</text>
                <text class="detail-item">类型: {{item.type || '未知'}}</text>
                <text class="detail-item">规格尺寸: {{item.spec || '未知'}}</text>
                <text class="detail-item">流程类型: {{item.flow_type || '未知'}}</text>
                <text class="detail-item">投产时间: {{item.operation_date || '未知'}}</text>
                <text class="detail-item">法兰形式: {{item.flange_type || '未知'}}</text>
                <text class="detail-item">法兰孔数: {{item.flange_holes || '未知'}}</text>
                <text class="detail-item">密封面形式: {{item.seal_type || '未知'}}</text>
                <text class="detail-item">垫子类型: {{item.gasket_type || '未知'}}</text>
                <text class="detail-item full-width" wx:if="{{item.remark}}">备注: {{item.remark}}</text>
              </view>
            </view>
          </view>
          <view class="no-data" wx:else>未找到匹配的阀门号</view>
        </view>

        <!-- 无结果提示 -->
        <view class="no-result" wx:if="{{noValveResult}}">
          <text>未找到匹配的数据</text>
          <view class="contact-admin">
            <text>如果您确认阀门号信息正确但无法查询，请联系管理员</text>
            <text class="contact-info">联系方式：管理员微信或电话</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 返回顶部按钮 -->
    <view class="back-to-top {{showBackToTop ? 'show' : ''}}" bindtap="scrollToTop">
      <text class="back-to-top-text">返回顶部</text>
    </view>
  </view>

  <!-- 修改密码弹窗 -->
  <view class="modal-mask" wx:if="{{showPasswordModal}}" bindtap="hideChangePassword"></view>
  <view class="modal-dialog" wx:if="{{showPasswordModal}}">
    <view class="modal-title">修改密码</view>
    <view class="modal-content">
      <input class="modal-input" type="password" placeholder="请输入原密码" model:value="{{oldPassword}}" />
      <input class="modal-input" type="password" placeholder="请输入新密码（至少6位）" model:value="{{newPassword}}" />
      <input class="modal-input" type="password" placeholder="请确认新密码" model:value="{{confirmPassword}}" />
      <view class="password-tip">密码长度不能少于6位</view>
    </view>
    <view class="modal-footer">
      <view class="modal-btn cancel" bindtap="hideChangePassword">取消</view>
      <view class="modal-btn confirm" bindtap="handleChangePassword">确定</view>
    </view>
  </view>
</view> 