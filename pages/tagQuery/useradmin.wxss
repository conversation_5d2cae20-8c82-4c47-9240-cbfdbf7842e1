.container {
  padding: 30rpx;
  background-color: #f6f7f9;
  min-height: 100vh;
  box-sizing: border-box;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  text-align: center;
}

.add-btn {
  background-color: #3498db;
  color: white;
  padding: 14rpx 28rpx;
  border-radius: 30rpx;
  font-size: 28rpx;
}

.search-box {
  background-color: white;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-input {
  width: 100%;
  height: 80rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 0 20rpx;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}

.user-list {
  width: 100% !important;
  background-color: #ffffff !important;
  border-radius: 10rpx !important;
  overflow: hidden !important;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  margin-bottom: 30rpx !important;
}

.list-header {
  display: flex;
  height: 80rpx;
  background-color: #f0f0f0;
  font-weight: bold;
  border-bottom: 1rpx solid #e0e0e0;
  text-align: center;
  align-items: center;
}

.user-item {
  display: flex !important;
  height: 100rpx !important;
  border-bottom: 1rpx solid #eeeeee !important;
  align-items: center !important;
}

.col-username, .col-role, .col-actions {
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
}

.col-username {
  flex: 2;
}

.col-role {
  flex: 2;
}

.col-actions {
  flex: 1;
}

.role-tag {
  display: inline-block !important;
  padding: 6rpx 20rpx !important;
  border-radius: 20rpx !important;
  font-size: 28rpx !important;
  color: #ffffff !important;
  text-align: center !important;
  min-width: 120rpx !important;
  margin: 0 auto !important;
  box-sizing: border-box !important;
}

.role-tag.admin {
  background-color: #e74c3c !important;
}

.role-tag.manager {
  background-color: #f39c12 !important;
}

.role-tag.user {
  background-color: #2ecc71 !important;
}

.action-btn {
  width: 80rpx !important;
  height: 50rpx !important;
  line-height: 50rpx !important;
  background-color: #1aad19 !important;
  color: #ffffff !important;
  font-size: 26rpx !important;
  border-radius: 10rpx !important;
  text-align: center !important;
}

.empty-state {
  padding: 80rpx 0;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  color: #999;
  font-size: 30rpx;
}

.loading {
  padding: 40rpx 0;
  text-align: center;
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 弹窗样式 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
}

.modal-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  max-width: 600rpx;
  background: white;
  border-radius: 16rpx;
  overflow: hidden;
  z-index: 1001;
  box-sizing: border-box;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  padding: 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-content {
  padding: 30rpx;
  box-sizing: border-box;
}

.modal-input {
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.role-picker {
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
}

.picker-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.picker-label {
  color: #999;
  margin-right: 10rpx;
}

.picker-value {
  color: #333;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #e0e0e0;
  justify-content: space-between;
  padding: 20rpx 30rpx;
}

.modal-footer .btn {
  width: 45%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  border-radius: 8rpx;
  flex: none;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-footer .btn-cancel {
  background-color: #f0f0f0;
  color: #333;
}

.modal-footer .btn-confirm {
  background-color: #07c160;
  color: #fff;
}

/* 添加用户按钮 */
.btn-add {
  width: 100%;
  height: 80rpx;
  background-color: #07c160;
  color: #fff;
  text-align: center;
  line-height: 80rpx;
  border-radius: 10rpx;
  margin-bottom: 30rpx;
}

/* 操作按钮 */
.mini-btn {
  font-size: 24rpx !important;
  background-color: #1aad19 !important;
  color: #fff !important;
  padding: 0 20rpx !important;
  height: 60rpx !important;
  line-height: 60rpx !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin: 0 auto !important;
  min-width: 80rpx !important;
}

/* 模态框 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.modal-content {
  width: 80%;
  background-color: #fff;
  border-radius: 10rpx;
  box-sizing: border-box;
  overflow: hidden;
}

.modal-header {
  position: relative;
  padding: 30rpx;
  border-bottom: 1rpx solid #e0e0e0;
}

.modal-close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  font-size: 36rpx;
  color: #888;
}

.modal-body {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.role-radio-group {
  display: flex;
  flex-direction: column;
}

.role-radio {
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.modal-footer {
  display: flex;
  border-top: 1rpx solid #e0e0e0;
}

.btn {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 32rpx;
}

.btn-cancel {
  background-color: #f0f0f0;
  color: #333;
}

.btn-confirm {
  background-color: #07c160;
  color: #fff;
}

/* 角色选择部分 */
.user-info {
  font-size: 32rpx;
  margin-bottom: 40rpx;
  text-align: center;
  font-weight: bold;
  color: #333;
}

.role-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
}

.role-option {
  padding: 20rpx 0;
  border-radius: 8rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  transition: all 0.3s;
  border: 2rpx solid transparent;
}

.role-option.selected {
  background-color: rgba(0, 0, 0, 0.05);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 角色标签在选择弹窗中的样式 */
.role-selection .role-tag {
  padding: 12rpx 30rpx !important;
  border-radius: 30rpx !important;
  font-size: 30rpx !important;
  min-width: 200rpx !important;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s;
}

.role-option.selected .role-tag {
  transform: scale(1.05);
}

/* 适配较小屏幕 */
@media screen and (max-width: 375px) {
  .col-username {
    flex: 1.5;
  }
  
  .col-role {
    flex: 1.5;
  }
  
  .role-tag {
    font-size: 22rpx;
    padding: 0 10rpx;
  }
}

/* 角色卡片选择器 */
.role-cards {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;
}

.role-card {
  background-color: #f5f5f5;
  border-radius: 10rpx;
  padding: 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.role-card.selected {
  background-color: #ffffff;
  border: 2rpx solid #e0e0e0;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  transform: translateY(-4rpx);
}

.role-card .role-tag {
  width: 80%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx !important;
  border-radius: 35rpx !important;
  margin: 0 auto;
}

/* 超简单的单选样式 */
.simple-radio-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.simple-radio {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.simple-radio radio {
  margin-left: 30rpx;
}

.simple-radio-text {
  margin-left: 20rpx;
  font-size: 28rpx;
  font-weight: bold;
  padding: 6rpx 20rpx;
  border-radius: 4rpx;
}

.simple-radio-text.user {
  color: #2ecc71;
}

.simple-radio-text.manager {
  color: #f39c12;
}

.simple-radio-text.admin {
  color: #e74c3c;
} 