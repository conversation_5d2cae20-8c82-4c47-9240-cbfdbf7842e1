{"appid": "wx6aa87efc3d1e3605", "compileType": "miniprogram", "libVersion": "3.8.9", "packOptions": {"ignore": [], "include": []}, "setting": {"enableUpdateWxAppCode": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "minified": true, "postcss": true, "es6": true, "enhance": true, "minifyWXSS": true, "condition": true, "ignoreUploadUnusedFiles": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "description": "项目配置文件，详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "projectArchitecture": "multiPlatform", "simulatorPluginLibVersion": {"wxext14566970e7e9f62": "3.6.5-37"}, "projectname": "miniprogram-2"}