<?php
/**
 * 证件照人像分割API
 * 使用腾讯云人体分析API进行人像抠图
 */

// 引入配置文件
require_once __DIR__ . '/config.php';
// 引入API统计记录器
require_once __DIR__ . '/api_stats_logger.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'code' => -1,
        'message' => '只支持POST请求'
    ]);
    exit();
}

// 腾讯云API配置已在config.php中定义

$startTime = microtime(true);
$requestSize = strlen(file_get_contents('php://input'));
$isSuccess = true;
$errorMessage = null;
$tencentRequestId = null;

try {
    // 获取POST数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data || !isset($data['image'])) {
        throw new Exception('缺少图片数据');
    }

    $imageBase64 = $data['image'];

    // 验证base64图片数据
    if (empty($imageBase64)) {
        throw new Exception('图片数据为空');
    }

    // 检查图片大小（base64编码后约为原图的4/3）
    if (strlen($imageBase64) > 10 * 1024 * 1024) {
        throw new Exception('图片过大，请选择小于8MB的图片');
    }

    // 图片质量预处理
    $processedImage = preprocessImage($imageBase64);

    // 调用腾讯云人像分割API
    $result = callTencentPortraitAPI($processedImage);

    // 获取腾讯云请求ID
    if (isset($result['RequestId'])) {
        $tencentRequestId = $result['RequestId'];
    }

    // 验证是否检测到有效人像
    if (!validatePortraitDetection($result)) {
        throw new Exception('未检测到有效的人像，请上传包含清晰人像的照片');
    }

    // 准备返回结果
    $response = [
        'code' => 0,
        'message' => '处理成功',
        'data' => [
            'resultImage' => $result['ResultImage'],
            'resultMask' => $result['ResultMask']
        ]
    ];

    // 记录API调用统计
    $responseSize = strlen(json_encode($response));
    logApiUsage('portrait_segmentation', 'SegmentPortraitPic', $startTime, true, null, $requestSize, $responseSize, $tencentRequestId);

    // 返回成功结果
    echo json_encode($response);

} catch (Exception $e) {
    $isSuccess = false;
    $errorMessage = $e->getMessage();



    // 准备返回结果
    $response = [
        'code' => -1,
        'message' => $e->getMessage()
    ];

    // 记录API调用统计（失败）
    $responseSize = strlen(json_encode($response));
    logApiUsage('portrait_segmentation', 'SegmentPortraitPic', $startTime, false, $errorMessage, $requestSize, $responseSize, $tencentRequestId);

    // 返回错误信息
    echo json_encode($response);
}

/**
 * 验证是否检测到有效人像
 * 通过分析mask图像来判断是否有足够的人像区域
 */
function validatePortraitDetection($result) {
    if (!isset($result['ResultMask']) || empty($result['ResultMask'])) {
        return false;
    }

    try {
        // 解码mask图像
        $maskData = base64_decode($result['ResultMask']);
        if ($maskData === false) {
            return false;
        }

        // 创建临时文件来处理mask图像
        $tempFile = tempnam(sys_get_temp_dir(), 'mask_');
        file_put_contents($tempFile, $maskData);

        // 使用GD库分析mask图像
        $maskImage = @imagecreatefromstring($maskData);
        if ($maskImage === false) {
            unlink($tempFile);
            return false;
        }

        $width = imagesx($maskImage);
        $height = imagesy($maskImage);
        $totalPixels = $width * $height;
        $portraitPixels = 0;

        // 统计人像像素（非黑色像素）
        $maxBrightness = 0;
        for ($x = 0; $x < $width; $x += 5) { // 采样检测，提高性能
            for ($y = 0; $y < $height; $y += 5) {
                $rgb = imagecolorat($maskImage, $x, $y);
                $r = ($rgb >> 16) & 0xFF;
                $g = ($rgb >> 8) & 0xFF;
                $b = $rgb & 0xFF;

                $brightness = ($r + $g + $b) / 3;
                $maxBrightness = max($maxBrightness, $brightness);

                // 如果不是纯黑色，认为是人像区域
                if ($brightness > 15) { // 提高阈值，减少噪点影响
                    $portraitPixels++;
                }
            }
        }

        imagedestroy($maskImage);
        unlink($tempFile);

        // 计算人像区域占比（考虑采样率）
        $sampledTotalPixels = ($width / 5) * ($height / 5);
        $portraitRatio = $portraitPixels / $sampledTotalPixels;

        // 多重验证条件：
        // 1. 人像区域占比至少3%
        // 2. mask图像中至少有一定亮度的像素（说明有实际内容）
        return $portraitRatio >= 0.03 && $maxBrightness > 50;

    } catch (Exception $e) {
        // 如果验证过程出错，为了安全起见返回false
        return false;
    }
}

/**
 * 调用腾讯云人像分割API
 */
function callTencentPortraitAPI($imageBase64) {
    $action = 'SegmentPortraitPic';  // 正确的Action名称
    $version = '2020-03-24';         // 人体分析的正确版本
    $timestamp = time();
    $date = gmdate('Y-m-d', $timestamp);
    
    // 请求参数
    $params = [
        'Image' => $imageBase64,
        'RspImgType' => 'base64'
    ];
    
    // 构建请求
    $payload = json_encode($params);
    
    // 生成签名
    $signature = generateTencentSignature($action, $version, $timestamp, $date, $payload);
    
    // 构建请求头
    $headers = [
        'Authorization: ' . $signature,
        'Content-Type: application/json; charset=utf-8',
        'Host: ' . BDA_HOST,
        'X-TC-Action: ' . $action,
        'X-TC-Timestamp: ' . $timestamp,
        'X-TC-Version: ' . $version,
        'X-TC-Region: ' . TENCENT_REGION
    ];
    
    // 发送请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://' . BDA_HOST);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception('网络请求失败: ' . $error);
    }
    
    if ($httpCode !== 200) {
        throw new Exception('API请求失败，HTTP状态码: ' . $httpCode);
    }
    
    $result = json_decode($response, true);
    
    if (!$result) {
        throw new Exception('API响应解析失败');
    }
    
    if (isset($result['Response']['Error'])) {
        $errorCode = $result['Response']['Error']['Code'];
        $errorMessage = $result['Response']['Error']['Message'];
        throw new Exception("腾讯云API错误: {$errorCode} - {$errorMessage}");
    }
    
    if (!isset($result['Response']['ResultImage'])) {
        throw new Exception('API返回数据格式错误');
    }
    
    return $result['Response'];
}

/**
 * 生成腾讯云API签名
 */
function generateTencentSignature($action, $version, $timestamp, $date, $payload) {
    $service = 'bda';  // 人体分析服务
    $algorithm = 'TC3-HMAC-SHA256';
    
    // 步骤1：拼接规范请求串
    $httpRequestMethod = 'POST';
    $canonicalUri = '/';
    $canonicalQueryString = '';
    $canonicalHeaders = "content-type:application/json; charset=utf-8\n" .
                       "host:" . BDA_HOST . "\n";
    $signedHeaders = 'content-type;host';
    $hashedRequestPayload = hash('sha256', $payload);
    
    $canonicalRequest = $httpRequestMethod . "\n" .
                       $canonicalUri . "\n" .
                       $canonicalQueryString . "\n" .
                       $canonicalHeaders . "\n" .
                       $signedHeaders . "\n" .
                       $hashedRequestPayload;
    
    // 步骤2：拼接待签名字符串
    $credentialScope = $date . '/' . $service . '/tc3_request';
    $hashedCanonicalRequest = hash('sha256', $canonicalRequest);
    
    $stringToSign = $algorithm . "\n" .
                   $timestamp . "\n" .
                   $credentialScope . "\n" .
                   $hashedCanonicalRequest;
    
    // 步骤3：计算签名
    $secretDate = hash_hmac('sha256', $date, 'TC3' . TENCENT_SECRET_KEY, true);
    $secretService = hash_hmac('sha256', $service, $secretDate, true);
    $secretSigning = hash_hmac('sha256', 'tc3_request', $secretService, true);
    $signature = hash_hmac('sha256', $stringToSign, $secretSigning);
    
    // 步骤4：拼接Authorization
    $authorization = $algorithm . ' ' .
                    'Credential=' . TENCENT_SECRET_ID . '/' . $credentialScope . ', ' .
                    'SignedHeaders=' . $signedHeaders . ', ' .
                    'Signature=' . $signature;
    
    return $authorization;
}

/**
 * 图片预处理，提高分割精度
 */
function preprocessImage($imageBase64) {
    try {
        // 解码base64图片
        $imageData = base64_decode($imageBase64);
        if ($imageData === false) {
            return $imageBase64; // 解码失败，返回原图
        }

        // 创建图片资源
        $image = imagecreatefromstring($imageData);
        if ($image === false) {
            return $imageBase64; // 创建失败，返回原图
        }

        $width = imagesx($image);
        $height = imagesy($image);

        // 如果图片太小，进行适当放大以提高分割精度
        if ($width < 400 || $height < 400) {
            $scale = max(400 / $width, 400 / $height);
            $newWidth = (int)($width * $scale);
            $newHeight = (int)($height * $scale);

            $scaledImage = imagecreatetruecolor($newWidth, $newHeight);
            imageantialias($scaledImage, true);
            imagecopyresampled($scaledImage, $image, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);

            imagedestroy($image);
            $image = $scaledImage;
            $width = $newWidth;
            $height = $newHeight;
        }

        // 创建高质量的处理后图片
        $processedImage = imagecreatetruecolor($width, $height);

        // 启用抗锯齿
        imageantialias($processedImage, true);

        // 复制并优化图片
        imagecopyresampled($processedImage, $image, 0, 0, 0, 0, $width, $height, $width, $height);

        // 增强对比度，提高人像与背景的区分度
        imagefilter($processedImage, IMG_FILTER_CONTRAST, 10);

        // 轻微锐化处理，提高边缘清晰度
        $sharpenMatrix = [
            [0, -1, 0],
            [-1, 5, -1],
            [0, -1, 0]
        ];
        $divisor = 1;
        $offset = 0;
        imageconvolution($processedImage, $sharpenMatrix, $divisor, $offset);

        // 减少噪点
        imagefilter($processedImage, IMG_FILTER_SMOOTH, 1);

        // 输出为JPEG格式（更小的文件大小）
        ob_start();
        imagejpeg($processedImage, null, 95); // 高质量JPEG
        $processedData = ob_get_contents();
        ob_end_clean();

        // 清理资源
        imagedestroy($image);
        imagedestroy($processedImage);

        // 返回处理后的base64
        return base64_encode($processedData);

    } catch (Exception $e) {
        // 处理失败，返回原图
        return $imageBase64;
    }
}

?>
