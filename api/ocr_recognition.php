<?php
/**
 * 简化版腾讯云OCR文字识别API
 * 专注于提取所有防爆标识
 */

// 引入配置文件
require_once __DIR__ . '/config.php';
// 引入API统计记录器
require_once __DIR__ . '/api_stats_logger.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

// 腾讯云API配置已在config.php中定义

/**
 * 裁剪图片
 */
function cropImage($imageBase64, $cropInfo) {
    try {
        // 解码base64图片
        $imageData = base64_decode($imageBase64);
        if ($imageData === false) {
            throw new Exception('图片数据解码失败');
        }

        // 创建图片资源
        $image = imagecreatefromstring($imageData);
        if ($image === false) {
            throw new Exception('图片资源创建失败');
        }

        // 获取原图尺寸
        $originalWidth = imagesx($image);
        $originalHeight = imagesy($image);

        // 验证裁剪参数
        $x = max(0, min($originalWidth - 1, intval($cropInfo['x'])));
        $y = max(0, min($originalHeight - 1, intval($cropInfo['y'])));
        $width = max(1, min($originalWidth - $x, intval($cropInfo['width'])));
        $height = max(1, min($originalHeight - $y, intval($cropInfo['height'])));

        // 创建裁剪后的图片
        $croppedImage = imagecreatetruecolor($width, $height);
        if ($croppedImage === false) {
            imagedestroy($image);
            throw new Exception('裁剪图片创建失败');
        }

        // 执行裁剪
        if (!imagecopy($croppedImage, $image, 0, 0, $x, $y, $width, $height)) {
            imagedestroy($image);
            imagedestroy($croppedImage);
            throw new Exception('图片裁剪失败');
        }

        // 输出为JPEG格式
        ob_start();
        if (!imagejpeg($croppedImage, null, 90)) {
            imagedestroy($image);
            imagedestroy($croppedImage);
            ob_end_clean();
            throw new Exception('图片输出失败');
        }
        $croppedImageData = ob_get_contents();
        ob_end_clean();

        // 清理资源
        imagedestroy($image);
        imagedestroy($croppedImage);

        // 返回base64编码的裁剪图片
        return base64_encode($croppedImageData);

    } catch (Exception $e) {
        // 裁剪失败时返回原图
        error_log('图片裁剪失败: ' . $e->getMessage());
        return $imageBase64;
    }
}

/**
 * 调用腾讯云OCR API
 */
function callTencentOCR($imageBase64) {
    $secretId = TENCENT_SECRET_ID;
    $secretKey = TENCENT_SECRET_KEY;
    $region = TENCENT_REGION;
    $endpoint = OCR_ENDPOINT;
    
    // API参数
    $action = 'GeneralBasicOCR';
    $version = '2018-11-19';
    $timestamp = time();
    $date = gmdate('Y-m-d', $timestamp);
    
    // 请求体
    $payload = json_encode([
        'ImageBase64' => $imageBase64,
        'LanguageType' => 'auto',
        'IsPdf' => false,
        'PdfPageNumber' => 1,
        'IsWords' => true
    ]);
    
    // 构建签名字符串
    $httpRequestMethod = 'POST';
    $canonicalUri = '/';
    $canonicalQueryString = '';
    $canonicalHeaders = "content-type:application/json; charset=utf-8\n" .
                       "host:" . $endpoint . "\n" .
                       "x-tc-action:" . strtolower($action) . "\n" .
                       "x-tc-timestamp:" . $timestamp . "\n" .
                       "x-tc-version:" . $version . "\n";
    $signedHeaders = 'content-type;host;x-tc-action;x-tc-timestamp;x-tc-version';
    
    $hashedRequestPayload = hash('sha256', $payload);
    
    $canonicalRequest = $httpRequestMethod . "\n" .
                       $canonicalUri . "\n" .
                       $canonicalQueryString . "\n" .
                       $canonicalHeaders . "\n" .
                       $signedHeaders . "\n" .
                       $hashedRequestPayload;
    
    $algorithm = 'TC3-HMAC-SHA256';
    $credentialScope = $date . '/' . 'ocr' . '/' . 'tc3_request';
    $stringToSign = $algorithm . "\n" .
                   $timestamp . "\n" .
                   $credentialScope . "\n" .
                   hash('sha256', $canonicalRequest);
    
    // 计算签名
    $secretDate = hash_hmac('sha256', $date, 'TC3' . $secretKey, true);
    $secretService = hash_hmac('sha256', 'ocr', $secretDate, true);
    $secretSigning = hash_hmac('sha256', 'tc3_request', $secretService, true);
    $signature = hash_hmac('sha256', $stringToSign, $secretSigning);
    
    // 构建Authorization
    $authorization = $algorithm . ' ' .
                    'Credential=' . $secretId . '/' . $credentialScope . ', ' .
                    'SignedHeaders=' . $signedHeaders . ', ' .
                    'Signature=' . $signature;
    
    // 发送请求
    $headers = [
        'Authorization: ' . $authorization,
        'Content-Type: application/json; charset=utf-8',
        'Host: ' . $endpoint,
        'X-TC-Action: ' . $action,
        'X-TC-Timestamp: ' . $timestamp,
        'X-TC-Version: ' . $version,
        'X-TC-Region: ' . $region
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://' . $endpoint);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception('CURL错误: ' . $error);
    }
    
    if ($httpCode !== 200) {
        throw new Exception('HTTP错误: ' . $httpCode);
    }
    
    $result = json_decode($response, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('响应JSON解析失败: ' . json_last_error_msg());
    }
    
    return $result;
}

/**
 * OCR错误修正
 */
function correctOCRErrors($text) {
    $corrections = [
        // Ex前缀修正 - 处理任意字符+Ex的情况
        // 匹配单个字符+Ex，但要确保不是有效的防爆类型前缀
        '/\b[^a-zA-Z\s]Ex\b/i' => 'Ex',  // 数字或特殊字符+Ex -> Ex
        '/\b[0-9]Ex\b/i' => 'Ex',  // 数字+Ex -> Ex
        '/\b[JILTO1]Ex\b/i' => 'Ex',  // 常见误识别字母+Ex -> Ex

        // 处理可能的防爆类型误识别（保留有效的防爆类型）
        '/\b[^a-zA-Z\s]Ex([a-z]{1,4})\b/i' => 'Ex$1',  // 错误字符+Ex+类型 -> Ex+类型
        '/\b[0-9]Ex([a-z]{1,4})\b/i' => 'Ex$1',  // 数字+Ex+类型 -> Ex+类型
        '/\b[JILTO1]Ex([a-z]{1,4})\b/i' => 'Ex$1',  // 误识别字母+Ex+类型 -> Ex+类型

        // 特定的Ex类型修正
        '/\bEXTB\b/i' => 'Ex tb',
        '/\bEXDB\b/i' => 'Ex db',
        '/\bExtD\b/i' => 'Ex tD',  // ExtD -> Ex tD
        '/\bEXTD\b/i' => 'Ex tD',  // EXTD -> Ex tD
        '/\bExtb\s*IIIC\b/i' => 'Ex tb IIIC',  // 外壳保护型+导电性粉尘
        '/\bExdb\s*IIB\b/i' => 'Ex db IIB',

        // 温度修正 - 处理空格分离的情况
        '/\bT\s*(\d+)\s*°?\s*C\b/i' => 'T$1°C',  // T 95 C -> T95°C
        '/\bT\s*(\d+)\s*°?C?\b/i' => 'T$1°C',  // T 5 -> T5°C
        '/\bT(\d+)C\b/i' => 'T$1°C',  // T80C -> T80°C
        '/\bT\s*(\d+)\s*\'/i' => 'T$1°',  // T 95' -> T95°

        // EPL等级修正
        '/\bGb([A-Z])/i' => 'Gb $1',  // GbExtD -> Gb ExtD
        '/\bDb([A-Z])/i' => 'Db $1',  // DbExtD -> Db ExtD

        // 粉尘防爆类型修正
        '/\btD\s*([AB]\d{2})/i' => 'tD $1',  // tDA21 -> tD A21
        '/\bmD\s*([AB]\d{2})/i' => 'mD $1',  // mDA21 -> mD A21
        '/\bEx\s*tD([AB]\d{2})/i' => 'Ex tD $1',  // Ex tDA21 -> Ex tD A21
        '/\bEx\s*mD([AB]\d{2})/i' => 'Ex mD $1',  // Ex mDA21 -> Ex mD A21
        '/\btD\s*-\s*([AB]\d{2})/i' => 'tD $1',  // tD -A21 -> tD A21
        '/\bmD\s*-\s*([AB]\d{2})/i' => 'mD $1',  // mD -A21 -> mD A21
        '/\bEx\s*tD\s*-\s*([AB]\d{2})/i' => 'Ex tD $1',  // Ex tD -A21 -> Ex tD A21

        // 区域级别修正
        '/\b([AB])(\d{2})\s*IP/i' => '$1$2 IP',  // A21IP -> A21 IP
        '/\bIP\s*(\d{2})\s*T/i' => 'IP$1 T',  // IP 66 T -> IP66 T

        // 设备类别修正 - 优先处理带温度的情况
        '/\b(I{1,3}[ABC]?)T\s*(\d+)/i' => '$1 T$2',  // IIBT5 -> IIB T5
        '/\b(I{1,3}[ABC]?)\s*T\s*(\d+)/i' => '$1 T$2',  // IIBT 5 -> IIB T5
        // 只处理不带温度的设备类别空格问题
        '/\b(I{1,3})\s*([ABC])\s*T(?!\s*\d)/i' => '$1$2T',  // IIB T -> IIBT (但不影响 IIBT 5)
    ];

    foreach ($corrections as $pattern => $replacement) {
        if (is_callable($replacement)) {
            $text = preg_replace_callback($pattern, $replacement, $text);
        } else {
            $text = preg_replace($pattern, $replacement, $text);
        }
    }

    return $text;
}

/**
 * 查找所有防爆标识
 */
function findAllExplosionProofCodes($text) {
    $foundCodes = [];

    // 防爆标识的各种格式模式（更宽松的匹配）
    $patterns = [
        // 最优先：专门处理 Ex d IIBT 5 Gb 这种格式
        '/Ex\s+d\s+IIBT\s+5\s+Gb/i',
        '/Ex\s+d\s+IIBT\s*\d+\s+Gb/i',
        '/Ex\s+d\s+I{1,3}[ABC]?T\s*\d+\s+[GDM][abc]/i',
        '/Ex\s+[a-z]+\s+I{1,3}[ABC]?T\s*\d+\s+[GDM][abc]/i',

        // 处理 ExtD -A21 这种OCR错误格式
        '/ExtD\s*-\s*A21\s+IP66\s+T95°C/i',
        '/ExtD\s*-\s*A\d{2}\s+IP\d{2}\s+T\d+°C/i',

        // 最简单的 tD A21 测试模式 - 处理空格分离的温度
        '/Ex\s+tD\s+A21\s+IP66\s+T95\s*°?\s*C/i',
        '/Ex\s+tD\s+A\d{2}\s+IP\d{2}\s+T\d+\s*°?\s*C/i',
        '/Ex\s+tD\s+A21\s+IP66\s+T95°C/i',
        '/Ex\s+tD\s+A\d{2}\s+IP\d{2}\s+T\d+°C/i',

        // 新标准完整格式（包含EPL等级）- 更宽松
        '/Ex\s*[a-zA-Z]{2,4}\s*I{1,3}[ABC]?\s*T\d+°?C?\s*[A-Z][a-z]?/i',
        // 无空格新标准格式
        '/Ex[a-zA-Z]{2,4}I{1,3}[ABC]?\s*T\d+°?C?\s*[A-Z][a-z]?/i',

        // 专门针对 tD A21 格式的模式 - 处理空格分离的温度
        '/Ex\s+tD\s+A[0-9]{2}\s+IP\d+\s+T\d+\s*°?\s*C/i',
        '/Ex\s+tD\s+B[0-9]{2}\s+IP\d+\s+T\d+\s*°?\s*C/i',
        '/Ex\s+mD\s+A[0-9]{2}\s+IP\d+\s+T\d+\s*°?\s*C/i',
        '/Ex\s+mD\s+B[0-9]{2}\s+IP\d+\s+T\d+\s*°?\s*C/i',
        // 标准格式版本
        '/Ex\s+tD\s+A[0-9]{2}\s+IP\d+\s+T\d+°?C?/i',
        '/Ex\s+tD\s+B[0-9]{2}\s+IP\d+\s+T\d+°?C?/i',
        '/Ex\s+mD\s+A[0-9]{2}\s+IP\d+\s+T\d+°?C?/i',
        '/Ex\s+mD\s+B[0-9]{2}\s+IP\d+\s+T\d+°?C?/i',
        // 无空格版本
        '/Ex\s*tD\s*A[0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/Ex\s*tD\s*B[0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/Ex\s*mD\s*A[0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/Ex\s*mD\s*B[0-9]{2}\s*IP\d+\s*T\d+°?C?/i',

        // 新标准粉尘区域格式（包含A21、B21等）
        '/Ex\s*[a-zA-Z]{2,4}\s*[AB][0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/Ex\s*[a-zA-Z]{2,4}\s*I{1,3}[ABC]?\s*[AB][0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/Ex\s*[a-zA-Z]{2,4}\s*[AB][0-9]{2}\s*T\d+°?C?/i',

        // 旧标准格式
        '/Ex\s*[a-zA-Z]{1,3}\s*I{1,3}[ABC]?\s*T\d+/i',
        // 更宽松的格式（可能缺少某些部分）
        '/Ex\s*[a-zA-Z]{1,4}\s*I{1,3}[ABC]?\s*T\d+[°C]*\s*[A-Z][a-z]?/i',

        // 处理可能被误识别的Ex前缀的模式
        '/[^a-zA-Z\s]Ex\s*[a-zA-Z]{1,4}\s*I{1,3}[ABC]?\s*T\d+[°C]*\s*[A-Z][a-z]?/i',
        '/[0-9]Ex\s*[a-zA-Z]{1,4}\s*I{1,3}[ABC]?\s*T\d+[°C]*\s*[A-Z][a-z]?/i',
        '/[JILTO1]Ex\s*[a-zA-Z]{1,4}\s*I{1,3}[ABC]?\s*T\d+[°C]*\s*[A-Z][a-z]?/i',

        // 处理包含区域级别的误识别前缀
        '/[^a-zA-Z\s]Ex\s*[a-zA-Z]{2,4}\s*[AB][0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/[0-9]Ex\s*[a-zA-Z]{2,4}\s*[AB][0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/[JILTO1]Ex\s*[a-zA-Z]{2,4}\s*[AB][0-9]{2}\s*IP\d+\s*T\d+°?C?/i',

        // 处理 tD 类型的误识别前缀
        '/[^a-zA-Z\s]Ex\s*tD\s*[AB][0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/[0-9]Ex\s*tD\s*[AB][0-9]{2}\s*IP\d+\s*T\d+°?C?/i',
        '/[JILTO1]Ex\s*tD\s*[AB][0-9]{2}\s*IP\d+\s*T\d+°?C?/i'
    ];

    // 对每种模式进行匹配
    foreach ($patterns as $pattern) {
        if (preg_match_all($pattern, $text, $matches)) {
            foreach ($matches[0] as $match) {
                // 对于包含错误前缀的匹配，先清理前缀
                $cleanedMatch = preg_replace('/^[^a-zA-Z\s]*Ex/i', 'Ex', $match);
                $cleanedMatch = preg_replace('/^[0-9]Ex/i', 'Ex', $cleanedMatch);
                $cleanedMatch = preg_replace('/^[JILTO1]Ex/i', 'Ex', $cleanedMatch);

                $cleaned = cleanExplosionProofCode($cleanedMatch);
                if ($cleaned && !in_array($cleaned, $foundCodes)) {
                    $foundCodes[] = $cleaned;
                }
            }
        }
    }

    // 如果没有找到完整的，尝试查找包含Ex的片段并手动解析
    if (empty($foundCodes)) {
        $foundCodes = findExplosionProofByParts($text);
    }

    // 去重：只保留最完整的版本
    $foundCodes = removeDuplicatesKeepComplete($foundCodes);

    return $foundCodes;
}

/**
 * 去重：只保留最完整的版本
 */
function removeDuplicatesKeepComplete($codes) {
    if (empty($codes)) return $codes;

    $filtered = [];

    foreach ($codes as $code) {
        $isSubset = false;

        // 检查当前代码是否是其他代码的子集
        foreach ($codes as $otherCode) {
            if ($code !== $otherCode && isSubsetOf($code, $otherCode)) {
                $isSubset = true;
                break;
            }
        }

        // 如果不是子集，检查是否已经有更完整的版本
        if (!$isSubset) {
            $shouldAdd = true;
            $filtered = array_filter($filtered, function($existingCode) use ($code, &$shouldAdd) {
                if (isSubsetOf($existingCode, $code)) {
                    // 当前代码更完整，移除已存在的不完整版本
                    return false;
                } elseif (isSubsetOf($code, $existingCode)) {
                    // 已存在更完整的版本，不添加当前代码
                    $shouldAdd = false;
                    return true;
                }
                return true;
            });

            if ($shouldAdd) {
                $filtered[] = $code;
            }
        }
    }

    return array_values($filtered);
}

/**
 * 检查code1是否是code2的子集
 */
function isSubsetOf($code1, $code2) {
    // 移除空格进行比较
    $clean1 = preg_replace('/\s+/', '', strtolower($code1));
    $clean2 = preg_replace('/\s+/', '', strtolower($code2));

    // 如果code1完全包含在code2中，且code2更长，则code1是code2的子集
    return strlen($clean1) < strlen($clean2) && strpos($clean2, $clean1) !== false;
}

/**
 * 按部分查找防爆标识（当完整匹配失败时）
 */
function findExplosionProofByParts($text) {
    $foundCodes = [];

    // 查找所有Ex开头的片段（包括可能被误识别前缀的）
    $patterns = [
        '/Ex[^,，。\n\r\/]{5,}/i',  // 标准Ex开头
        '/[^a-zA-Z\s]Ex[^,，。\n\r\/]{5,}/i',  // 特殊字符+Ex
        '/[0-9]Ex[^,，。\n\r\/]{5,}/i',  // 数字+Ex
        '/[JILTO1]Ex[^,，。\n\r\/]{5,}/i'  // 常见误识别字母+Ex
    ];

    foreach ($patterns as $pattern) {
        if (preg_match_all($pattern, $text, $matches)) {
            foreach ($matches[0] as $match) {
                // 清理可能的错误前缀
                $cleanedMatch = preg_replace('/^[^a-zA-Z\s]*Ex/i', 'Ex', $match);
                $cleanedMatch = preg_replace('/^[0-9]Ex/i', 'Ex', $cleanedMatch);
                $cleanedMatch = preg_replace('/^[JILTO1]Ex/i', 'Ex', $cleanedMatch);

                // 尝试从片段中提取有效的防爆标识
                $candidate = extractFromFragment($cleanedMatch, $text);
                if ($candidate && !in_array($candidate, $foundCodes)) {
                    $foundCodes[] = $candidate;
                }
            }
        }
    }

    return $foundCodes;
}

/**
 * 从文字片段中提取防爆标识
 */
function extractFromFragment($fragment, $fullText) {
    // 清理片段
    $fragment = trim($fragment);

    // 查找基本组件
    $hasEx = preg_match('/^Ex/i', $fragment);
    $hasType = preg_match('/Ex\s*([a-zA-Z]{1,4})/i', $fragment, $typeMatch);
    $hasCategory = preg_match('/(I{1,3}[ABC]?)/i', $fragment, $categoryMatch);
    $hasTemp = preg_match('/T(\d+)/i', $fragment, $tempMatch);

    if (!$hasEx || !$hasType || !$hasCategory || !$hasTemp) {
        return null;
    }

    // 构建基本标识
    $type = strtolower($typeMatch[1]);
    $category = strtoupper($categoryMatch[1]);
    $temp = 'T' . $tempMatch[1];

    $basic = "Ex $type $category $temp";

    // 查找EPL等级（可能在片段中或附近的文字中）
    $eplPattern = '/\b([GDM][abc])\b/i';
    if (preg_match($eplPattern, $fragment, $eplMatch)) {
        $epl = ucfirst(strtolower($eplMatch[1]));
        $basic .= " $epl";
    } else {
        // 在完整文字中查找可能的EPL等级
        $contextStart = max(0, strpos($fullText, $fragment) - 20);
        $contextEnd = min(strlen($fullText), strpos($fullText, $fragment) + strlen($fragment) + 20);
        $context = substr($fullText, $contextStart, $contextEnd - $contextStart);

        if (preg_match($eplPattern, $context, $eplMatch)) {
            $epl = ucfirst(strtolower($eplMatch[1]));
            $basic .= " $epl";
        }
    }

    // 处理温度符号
    if (preg_match('/T\d+°?C/i', $fragment)) {
        $basic = preg_replace('/T(\d+)/', 'T$1°C', $basic);
    }

    return $basic;
}

/**
 * 清理和格式化防爆标识
 */
function cleanExplosionProofCode($code) {
    if (empty($code)) return null;

    // 移除无关字符
    $code = preg_replace('/防爆标志/', '', $code);
    $code = preg_replace('/[，。、]/', '', $code);
    $code = trim($code);

    // 处理无空格格式（如ExdbIIB T6 Gb）
    $code = preg_replace('/Ex([a-zA-Z]{2,4})(I{1,3}[ABC]?)/i', 'Ex $1 $2', $code);

    // 规范化空格
    $code = preg_replace('/\s+/', ' ', $code);

    // 修正大小写 - 特殊处理 tD, mD 等混合大小写类型
    $code = preg_replace_callback('/Ex\s+([a-zA-Z]{1,4})/i', function($matches) {
        $type = $matches[1];
        // 保持特殊类型的大小写
        if (strtolower($type) === 'td') return 'Ex tD';
        if (strtolower($type) === 'md') return 'Ex mD';
        if (strtolower($type) === 'nl') return 'Ex nL';
        if (strtolower($type) === 'nz') return 'Ex nZ';
        // 其他类型转为小写
        return 'Ex ' . strtolower($type);
    }, $code);

    // 修正设备类别大小写
    $code = preg_replace_callback('/(I{1,3}[ABC]?)/i', function($matches) {
        return strtoupper($matches[1]);
    }, $code);

    // 修正温度标识 - 智能保留°C符号
    $code = preg_replace('/T(\d+)°?C?/i', 'T$1°C', $code);

    // 只有在明确是旧标准格式时才去掉°C
    // 旧标准特征：有设备类别(I{1,3}[ABC]?)且后面直接跟EPL等级
    if (preg_match('/Ex\s+[a-z]+\s+I{1,3}[ABC]?\s+T\d+°C\s+[GDM][abc]/i', $code)) {
        // 这是旧标准格式，去掉°C
        $code = preg_replace('/T(\d+)°C/i', 'T$1', $code);
    }
    // 对于粉尘格式(tD, mD)或区域级别(A21, B21)，保留°C
    // 对于新标准格式，保留°C

    // 修正EPL等级大小写
    $code = preg_replace_callback('/\b([GDM][abc])\b/i', function($matches) {
        return ucfirst(strtolower($matches[1]));
    }, $code);

    // 修正区域级别格式 (A21, B21等)
    $code = preg_replace_callback('/\b([AB])(\d{2})\b/i', function($matches) {
        return strtoupper($matches[1]) . $matches[2];
    }, $code);

    // 修正IP防护等级格式
    $code = preg_replace('/IP\s*(\d+)/i', 'IP$1', $code);

    $code = trim($code);

    // 宽松的验证格式 - 支持多种防爆标识格式
    $isValid = false;

    // 传统格式：Ex + 类型 + 设备类别 + 温度
    if (preg_match('/Ex\s+[a-zA-Z]{1,4}\s+I{1,3}[ABC]?\s+T\d+/i', $code)) {
        $isValid = true;
    }

    // 粉尘格式：Ex + tD/mD + 区域级别 + IP + 温度
    if (preg_match('/Ex\s+[tm]D\s+[AB]\d{2}\s+IP\d+\s+T\d+/i', $code)) {
        $isValid = true;
    }

    // 简化格式：只要包含Ex和温度就认为有效
    if (preg_match('/Ex\s+[a-zA-Z]{1,4}.*T\d+/i', $code)) {
        $isValid = true;
    }

    if (!$isValid) {
        return null;
    }

    return $code;
}

/**
 * 从OCR结果中提取所有防爆标识
 */
function extractExplosionProofCode($ocrData) {
    if (!isset($ocrData['Response']['TextDetections'])) {
        return null;
    }

    $allFoundCodes = [];

    // 方法1：分别处理每个OCR检测片段，避免跨片段错误组合
    foreach ($ocrData['Response']['TextDetections'] as $detection) {
        $correctedText = correctOCRErrors($detection['DetectedText']);
        $fragmentCodes = findAllExplosionProofCodes($correctedText);

        foreach ($fragmentCodes as $code) {
            if (!in_array($code, $allFoundCodes)) {
                $allFoundCodes[] = $code;
            }
        }
    }

    // 方法2：如果单独处理没找到，再尝试合并处理（但要更谨慎）
    if (empty($allFoundCodes)) {
        // 按行分组处理，避免距离太远的文字被错误组合
        $textByLines = [];
        foreach ($ocrData['Response']['TextDetections'] as $detection) {
            $polygon = $detection['Polygon'] ?? [];
            if (!empty($polygon)) {
                // 使用Y坐标分组（同一行的文字Y坐标相近）
                $avgY = 0;
                foreach ($polygon as $point) {
                    $avgY += $point['Y'];
                }
                $avgY = intval($avgY / count($polygon));

                $lineKey = intval($avgY / 20) * 20; // 20像素容差分组
                if (!isset($textByLines[$lineKey])) {
                    $textByLines[$lineKey] = [];
                }
                $textByLines[$lineKey][] = $detection['DetectedText'];
            }
        }

        // 在每行内查找防爆标识
        foreach ($textByLines as $lineTexts) {
            $lineText = implode(' ', $lineTexts);
            $correctedLineText = correctOCRErrors($lineText);
            $lineCodes = findAllExplosionProofCodes($correctedLineText);

            foreach ($lineCodes as $code) {
                if (!in_array($code, $allFoundCodes)) {
                    $allFoundCodes[] = $code;
                }
            }
        }
    }

    // 去重：只保留最完整的版本
    $allFoundCodes = removeDuplicatesKeepComplete($allFoundCodes);

    if (empty($allFoundCodes)) {
        return null;
    }

    // 如果找到多个，用斜杠连接
    if (count($allFoundCodes) > 1) {
        return implode(' / ', $allFoundCodes);
    }

    return $allFoundCodes[0];
}

// 主处理逻辑
$startTime = microtime(true);
$requestSize = strlen(file_get_contents('php://input'));
$isSuccess = true;
$errorMessage = null;
$tencentRequestId = null;

try {
    // 检查API调用次数限制
    $limitCheck = checkApiCallLimit('ocr_recognition', 1000);
    if (!$limitCheck['allowed']) {
        throw new Exception($limitCheck['message']);
    }

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('只支持POST请求');
    }

    $input = json_decode(file_get_contents('php://input'), true);

    if (!isset($input['image']) || empty($input['image'])) {
        throw new Exception('缺少图片数据');
    }

    $imageBase64 = $input['image'];

    // 检查是否有裁剪信息
    $cropInfo = $input['cropInfo'] ?? null;
    if ($cropInfo && isset($cropInfo['x'], $cropInfo['y'], $cropInfo['width'], $cropInfo['height'])) {
        $imageBase64 = cropImage($imageBase64, $cropInfo);
    }

    // 调用腾讯云OCR
    $ocrResult = callTencentOCR($imageBase64);

    // 获取腾讯云请求ID
    if (isset($ocrResult['Response']['RequestId'])) {
        $tencentRequestId = $ocrResult['Response']['RequestId'];
    }

    // 检查API响应
    if (isset($ocrResult['Response']['Error'])) {
        throw new Exception($ocrResult['Response']['Error']['Message']);
    }

    // 提取防爆标识
    $explosionProofCode = extractExplosionProofCode($ocrResult);

    // 获取所有文字
    $allText = '';
    foreach ($ocrResult['Response']['TextDetections'] as $detection) {
        $allText .= correctOCRErrors($detection['DetectedText']) . ' ';
    }

    // 提取防爆标识
    $allFoundCodes = findAllExplosionProofCodes(trim($allText));

    // 准备返回结果
    $result = [
        'code' => 0,
        'message' => 'OCR识别成功',
        'data' => [
            'explosionProofCode' => $explosionProofCode,
            'allText' => trim($allText),
            'textCount' => count($ocrResult['Response']['TextDetections'] ?? [])
        ]
    ];

    // 记录API调用统计
    $responseSize = strlen(json_encode($result, JSON_UNESCAPED_UNICODE));
    logApiUsage('ocr_recognition', 'GeneralBasicOCR', $startTime, true, null, $requestSize, $responseSize, $tencentRequestId);

    // 返回结果
    echo json_encode($result, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    $isSuccess = false;
    $errorMessage = $e->getMessage();

    $result = [
        'code' => -1,
        'message' => $e->getMessage(),
        'data' => null
    ];

    // 记录API调用统计（失败）
    $responseSize = strlen(json_encode($result, JSON_UNESCAPED_UNICODE));
    logApiUsage('ocr_recognition', 'GeneralBasicOCR', $startTime, false, $errorMessage, $requestSize, $responseSize, $tencentRequestId);

    echo json_encode($result, JSON_UNESCAPED_UNICODE);
}
?>
