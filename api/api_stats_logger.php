<?php
/**
 * API统计记录器
 * 用于记录腾讯云API调用统计信息
 */

// 引入数据库配置
require_once __DIR__ . '/../includes/config.php';

/**
 * 检查API调用次数限制
 *
 * @param string $apiName API名称
 * @param int $monthlyLimit 月度限制次数，默认1000
 * @return array 返回检查结果 ['allowed' => bool, 'current_count' => int, 'limit' => int, 'message' => string]
 */
function checkApiCallLimit($apiName, $monthlyLimit = 1000) {
    global $pdo;

    try {
        // 获取当前月份的开始和结束时间
        $currentMonth = date('Y-m');
        $monthStart = $currentMonth . '-01 00:00:00';
        $monthEnd = date('Y-m-t 23:59:59');

        // 查询当月该API的调用次数（只统计成功的调用）
        $sql = "SELECT COUNT(*) as call_count
                FROM api_usage_stats
                WHERE api_name = ?
                AND call_time >= ?
                AND call_time <= ?
                AND is_success = 1";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$apiName, $monthStart, $monthEnd]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        $currentCount = $result['call_count'] ?? 0;
        $allowed = $currentCount < $monthlyLimit;

        $message = $allowed
            ? "当前调用次数: {$currentCount}/{$monthlyLimit}"
            : "本月额度已用完，请下月再试。当前调用次数: {$currentCount}/{$monthlyLimit}";

        return [
            'allowed' => $allowed,
            'current_count' => $currentCount,
            'limit' => $monthlyLimit,
            'message' => $message,
            'month' => $currentMonth
        ];

    } catch (Exception $e) {
        error_log("检查API调用限制失败: " . $e->getMessage());
        // 出错时允许调用，避免影响正常功能
        return [
            'allowed' => true,
            'current_count' => 0,
            'limit' => $monthlyLimit,
            'message' => '无法检查调用限制，允许调用',
            'month' => date('Y-m')
        ];
    }
}

/**
 * 记录API调用统计
 *
 * @param string $apiName API名称 (ocr_recognition/image_compress/portrait_segmentation)
 * @param string $apiAction 具体的API动作
 * @param float $startTime 开始时间 (microtime(true))
 * @param bool $isSuccess 是否成功
 * @param string $errorMessage 错误信息
 * @param int $requestSize 请求大小(字节)
 * @param int $responseSize 响应大小(字节)
 * @param string $tencentRequestId 腾讯云请求ID
 */
function logApiUsage($apiName, $apiAction = null, $startTime = null, $isSuccess = true, $errorMessage = null, $requestSize = null, $responseSize = null, $tencentRequestId = null) {
    global $pdo;
    
    try {
        // 确保API统计表存在
        createApiStatsTableIfNotExists();
        
        // 计算处理时间
        $processingTime = null;
        if ($startTime !== null) {
            $processingTime = microtime(true) - $startTime;
        }
        
        // 获取客户端信息
        $ipAddress = getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
        
        // 插入统计记录
        $sql = "INSERT INTO api_usage_stats (
            api_name, api_action, call_time, ip_address, user_agent,
            request_size, response_size, processing_time, is_success,
            error_message, tencent_request_id
        ) VALUES (?, ?, NOW(), ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $apiName,
            $apiAction,
            $ipAddress,
            $userAgent,
            $requestSize,
            $responseSize,
            $processingTime,
            $isSuccess ? 1 : 0,
            $errorMessage,
            $tencentRequestId
        ]);
        
    } catch (Exception $e) {
        // 记录错误但不影响主要功能
        error_log("API统计记录失败: " . $e->getMessage());
    }
}

/**
 * 获取客户端真实IP地址
 */
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ips = explode(',', $_SERVER[$key]);
            $ip = trim($ips[0]);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                return $ip;
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
}

/**
 * 创建API统计表（如果不存在）
 */
function createApiStatsTableIfNotExists() {
    global $pdo;
    
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS `api_usage_stats` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `api_name` varchar(50) NOT NULL COMMENT 'API名称',
            `api_action` varchar(100) DEFAULT NULL COMMENT '具体的API动作',
            `call_time` datetime NOT NULL COMMENT '调用时间',
            `ip_address` varchar(45) DEFAULT NULL COMMENT '调用IP地址',
            `user_agent` text COMMENT '用户代理',
            `request_size` int(11) DEFAULT NULL COMMENT '请求大小(字节)',
            `response_size` int(11) DEFAULT NULL COMMENT '响应大小(字节)',
            `processing_time` decimal(8,3) DEFAULT NULL COMMENT '处理时间(秒)',
            `is_success` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否成功',
            `error_message` text COMMENT '错误信息',
            `tencent_request_id` varchar(100) DEFAULT NULL COMMENT '腾讯云请求ID',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_api_name` (`api_name`),
            KEY `idx_call_time` (`call_time`),
            KEY `idx_ip_address` (`ip_address`),
            KEY `idx_is_success` (`is_success`),
            KEY `idx_api_time` (`api_name`, `call_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API调用统计表'");
    } catch (PDOException $e) {
        error_log("创建API统计表失败: " . $e->getMessage());
    }
}

/**
 * 获取API使用统计
 * 
 * @param string $apiName 指定API名称，为空则获取所有
 * @param string $startDate 开始日期
 * @param string $endDate 结束日期
 * @return array 统计数据
 */
function getApiUsageStats($apiName = null, $startDate = null, $endDate = null) {
    global $pdo;
    
    try {
        $sql = "SELECT 
            api_name,
            DATE(call_time) as call_date,
            COUNT(*) as total_calls,
            SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) as success_calls,
            SUM(CASE WHEN is_success = 0 THEN 1 ELSE 0 END) as failed_calls,
            ROUND(AVG(processing_time), 3) as avg_processing_time,
            SUM(request_size) as total_request_size,
            SUM(response_size) as total_response_size
        FROM api_usage_stats
        WHERE api_name != 'error_report'";
        
        $params = [];
        
        if ($apiName) {
            $sql .= " AND api_name = ?";
            $params[] = $apiName;
        }
        
        if ($startDate) {
            $sql .= " AND DATE(call_time) >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $sql .= " AND DATE(call_time) <= ?";
            $params[] = $endDate;
        }
        
        $sql .= " GROUP BY api_name, DATE(call_time) ORDER BY call_date DESC, api_name";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (Exception $e) {
        error_log("获取API统计失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 获取API调用总览统计
 */
function getApiOverviewStats() {
    global $pdo;

    try {
        $sql = "SELECT
            api_name,
            COUNT(*) as total_calls,
            SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) as success_calls,
            SUM(CASE WHEN is_success = 0 THEN 1 ELSE 0 END) as failed_calls,
            ROUND((SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as success_rate,
            ROUND(AVG(processing_time), 3) as avg_processing_time,
            DATE(MIN(call_time)) as first_call,
            DATE(MAX(call_time)) as last_call
        FROM api_usage_stats
        GROUP BY api_name
        ORDER BY total_calls DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("获取API总览统计失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 获取API使用统计（带分页）
 *
 * @param string $apiName 指定API名称，为空则获取所有
 * @param string $startDate 开始日期
 * @param string $endDate 结束日期
 * @param int $page 页码
 * @param int $perPage 每页数量
 * @return array 统计数据
 */
function getApiUsageStatsWithPagination($apiName = null, $startDate = null, $endDate = null, $page = 1, $perPage = 20) {
    global $pdo;

    try {
        $sql = "SELECT
            api_name,
            DATE(call_time) as call_date,
            COUNT(*) as total_calls,
            SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) as success_calls,
            SUM(CASE WHEN is_success = 0 THEN 1 ELSE 0 END) as failed_calls,
            ROUND(AVG(processing_time), 3) as avg_processing_time,
            SUM(request_size) as total_request_size,
            SUM(response_size) as total_response_size,
            COUNT(DISTINCT ip_address) as unique_ips
        FROM api_usage_stats
        WHERE api_name != 'error_report'";

        $params = [];

        if ($apiName) {
            $sql .= " AND api_name = ?";
            $params[] = $apiName;
        }

        if ($startDate) {
            $sql .= " AND DATE(call_time) >= ?";
            $params[] = $startDate;
        }

        if ($endDate) {
            $sql .= " AND DATE(call_time) <= ?";
            $params[] = $endDate;
        }

        $sql .= " GROUP BY api_name, DATE(call_time) ORDER BY call_date DESC, api_name";

        // 添加分页
        $offset = ($page - 1) * $perPage;
        $sql .= " LIMIT ? OFFSET ?";
        $params[] = $perPage;
        $params[] = $offset;

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("获取API统计失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 获取API使用统计总数
 *
 * @param string $apiName 指定API名称，为空则获取所有
 * @param string $startDate 开始日期
 * @param string $endDate 结束日期
 * @return int 总记录数
 */
function getTotalApiUsageCount($apiName = null, $startDate = null, $endDate = null) {
    global $pdo;

    try {
        $sql = "SELECT COUNT(DISTINCT CONCAT(api_name, '_', DATE(call_time))) as total_count
        FROM api_usage_stats
        WHERE api_name != 'error_report'";

        $params = [];

        if ($apiName) {
            $sql .= " AND api_name = ?";
            $params[] = $apiName;
        }

        if ($startDate) {
            $sql .= " AND DATE(call_time) >= ?";
            $params[] = $startDate;
        }

        if ($endDate) {
            $sql .= " AND DATE(call_time) <= ?";
            $params[] = $endDate;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['total_count'] ?? 0;

    } catch (Exception $e) {
        error_log("获取API统计总数失败: " . $e->getMessage());
        return 0;
    }
}

/**
 * 获取IP地址统计
 *
 * @param string $startDate 开始日期
 * @param string $endDate 结束日期
 * @param int $limit 限制返回数量
 * @return array IP统计数据
 */
function getIpUsageStats($startDate = null, $endDate = null, $limit = 10) {
    global $pdo;

    try {
        $sql = "SELECT
            ip_address,
            COUNT(*) as total_calls,
            SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) as success_calls,
            SUM(CASE WHEN is_success = 0 THEN 1 ELSE 0 END) as failed_calls,
            ROUND((SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) / COUNT(*)) * 100, 2) as success_rate,
            COUNT(DISTINCT api_name) as api_types,
            DATE(MIN(call_time)) as first_call,
            DATE(MAX(call_time)) as last_call
        FROM api_usage_stats
        WHERE ip_address IS NOT NULL AND ip_address != ''";

        $params = [];

        if ($startDate) {
            $sql .= " AND DATE(call_time) >= ?";
            $params[] = $startDate;
        }

        if ($endDate) {
            $sql .= " AND DATE(call_time) <= ?";
            $params[] = $endDate;
        }

        $sql .= " GROUP BY ip_address ORDER BY total_calls DESC";

        if ($limit > 0) {
            $sql .= " LIMIT ?";
            $params[] = $limit;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("获取IP统计失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 获取最近的API调用记录（包含IP信息）
 *
 * @param int $limit 限制返回数量
 * @param string $apiName 指定API名称
 * @return array 最近调用记录
 */
function getRecentApiCalls($limit = 50, $apiName = null) {
    global $pdo;

    try {
        $sql = "SELECT
            api_name, api_action, call_time, ip_address,
            processing_time, is_success, error_message,
            request_size, response_size
        FROM api_usage_stats
        WHERE api_name != 'error_report'";

        $params = [];
        if ($apiName) {
            $sql .= " AND api_name = ?";
            $params[] = $apiName;
        }

        $sql .= " ORDER BY call_time DESC LIMIT ?";
        $params[] = $limit;

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("获取最近调用记录失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 获取前端错误统计
 *
 * @param string $startDate 开始日期
 * @param string $endDate 结束日期
 * @param int $limit 限制返回数量
 * @return array 错误统计数据
 */
function getFrontendErrorStats($startDate = null, $endDate = null, $limit = 20) {
    global $pdo;

    try {
        $sql = "SELECT
            error_type,
            api_name,
            COUNT(*) as error_count,
            COUNT(DISTINCT ip_address) as unique_ips,
            DATE(error_time) as error_date,
            MAX(error_time) as latest_error
        FROM frontend_error_reports
        WHERE 1=1";

        $params = [];

        if ($startDate) {
            $sql .= " AND DATE(error_time) >= ?";
            $params[] = $startDate;
        }

        if ($endDate) {
            $sql .= " AND DATE(error_time) <= ?";
            $params[] = $endDate;
        }

        $sql .= " GROUP BY error_type, api_name, DATE(error_time)
                 ORDER BY error_date DESC, error_count DESC";

        if ($limit > 0) {
            $sql .= " LIMIT ?";
            $params[] = $limit;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("获取前端错误统计失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 获取最近的前端错误记录
 *
 * @param int $limit 限制返回数量
 * @param string $errorType 错误类型筛选
 * @return array 最近错误记录
 */
function getRecentFrontendErrors($limit = 20, $errorType = null) {
    global $pdo;

    try {
        $sql = "SELECT
            error_type, error_message, api_name, error_details,
            ip_address, error_time
        FROM frontend_error_reports
        WHERE 1=1";

        $params = [];
        if ($errorType) {
            $sql .= " AND error_type = ?";
            $params[] = $errorType;
        }

        $sql .= " ORDER BY error_time DESC LIMIT ?";
        $params[] = $limit;

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("获取最近前端错误记录失败: " . $e->getMessage());
        return [];
    }
}

/**
 * 获取当月API使用情况摘要
 *
 * @param array $apiNames 要检查的API名称数组
 * @param int $monthlyLimit 月度限制次数
 * @return array API使用情况摘要
 */
function getMonthlyApiUsageSummary($apiNames = ['portrait_segmentation', 'ocr_recognition'], $monthlyLimit = 1000) {
    $summary = [];

    foreach ($apiNames as $apiName) {
        $limitCheck = checkApiCallLimit($apiName, $monthlyLimit);
        $summary[$apiName] = $limitCheck;
    }

    return $summary;
}

/**
 * 获取API使用历史统计（按月）
 *
 * @param string $apiName API名称
 * @param int $months 获取最近几个月的数据，默认6个月
 * @return array 月度使用统计
 */
function getMonthlyApiUsageHistory($apiName, $months = 6) {
    global $pdo;

    try {
        $sql = "SELECT
                    DATE_FORMAT(call_time, '%Y-%m') as month,
                    COUNT(*) as total_calls,
                    SUM(CASE WHEN is_success = 1 THEN 1 ELSE 0 END) as success_calls,
                    SUM(CASE WHEN is_success = 0 THEN 1 ELSE 0 END) as failed_calls
                FROM api_usage_stats
                WHERE api_name = ?
                AND call_time >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
                GROUP BY DATE_FORMAT(call_time, '%Y-%m')
                ORDER BY month DESC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([$apiName, $months]);

        return $stmt->fetchAll(PDO::FETCH_ASSOC);

    } catch (Exception $e) {
        error_log("获取月度API使用历史失败: " . $e->getMessage());
        return [];
    }
}
?>
