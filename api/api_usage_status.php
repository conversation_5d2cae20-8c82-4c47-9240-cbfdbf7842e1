<?php
/**
 * API使用状态查询接口
 * 用于查看当前月度API调用次数和限制状态
 */

// 引入API统计记录器
require_once __DIR__ . '/api_stats_logger.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode([
        'code' => -1,
        'message' => '只支持GET请求'
    ]);
    exit();
}

try {
    // 获取当月API使用情况摘要
    $apiNames = ['portrait_segmentation', 'ocr_recognition'];
    $monthlyLimit = 1000;
    $summary = getMonthlyApiUsageSummary($apiNames, $monthlyLimit);
    
    // 计算总使用量
    $totalUsed = 0;
    $totalLimit = 0;
    foreach ($summary as $apiData) {
        $totalUsed += $apiData['current_count'];
        $totalLimit += $apiData['limit'];
    }
    
    // 获取使用历史（最近3个月）
    $history = [];
    foreach ($apiNames as $apiName) {
        $history[$apiName] = getMonthlyApiUsageHistory($apiName, 3);
    }
    
    // 准备返回结果
    $response = [
        'code' => 0,
        'message' => '获取成功',
        'data' => [
            'current_month' => date('Y-m'),
            'summary' => $summary,
            'total_used' => $totalUsed,
            'total_limit' => $totalLimit,
            'usage_percentage' => $totalLimit > 0 ? round(($totalUsed / $totalLimit) * 100, 2) : 0,
            'history' => $history,
            'status' => $totalUsed >= $totalLimit ? 'exceeded' : ($totalUsed >= $totalLimit * 0.8 ? 'warning' : 'normal')
        ]
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => -1,
        'message' => '获取API使用状态失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
