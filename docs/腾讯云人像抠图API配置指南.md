# 腾讯云人像抠图API配置指南

## 📋 配置步骤

### 1. 开通腾讯云服务

1. **登录腾讯云控制台**
   - 访问：https://console.cloud.tencent.com/
   - 使用微信或QQ登录

2. **开通人体分析服务**
   ```
   控制台 → 产品与服务 → 人工智能 → 计算机视觉 → 人体分析
   ```

3. **找到人像分割服务**
   - 在人体分析页面找到"人像分割"
   - 点击"立即使用"开通服务

### 2. 获取API密钥

1. **进入访问管理**
   ```
   控制台 → 访问管理 → API密钥管理
   ```

2. **创建密钥**
   - 点击"新建密钥"
   - 记录生成的 `SecretId` 和 `SecretKey`
   - ⚠️ **重要**: 密钥信息请妥善保管，不要泄露

### 3. 配置PHP后端

1. **修改API文件**
   编辑 `api/portrait_segmentation.php` 文件：
   ```php
   // 替换为你的实际密钥
   define('TENCENT_SECRET_ID', 'AKIDxxxxxxxxxxxxxxxx');
   define('TENCENT_SECRET_KEY', 'xxxxxxxxxxxxxxxxxxxxxxx');
   ```

2. **上传到服务器**
   - 将 `api/portrait_segmentation.php` 上传到你的服务器
   - 确保PHP版本 >= 7.0
   - 确保开启了curl扩展

3. **测试API接口**
   ```bash
   curl -X POST https://your-domain.com/api/portrait_segmentation.php \
   -H "Content-Type: application/json" \
   -d '{"image":"base64_image_data_here"}'
   ```

### 4. 小程序端配置

1. **添加页面路径**
   在 `app.json` 中添加：
   ```json
   {
     "pages": [
       "pages/photoBackground/photoBackground"
     ]
   }
   ```

2. **配置服务器域名**
   在微信小程序后台配置request合法域名：
   ```
   https://your-domain.com
   ```

3. **更新API地址**
   如果你的域名不是 `sunxiyue.com`，需要修改：
   ```javascript
   // utils/portraitProcessor.js 第37行
   url: 'https://your-domain.com/api/portrait_segmentation.php'
   ```

## 💰 费用说明

### 腾讯云人像分割API计费

- **免费额度**: 每月1000次免费调用
- **超出费用**: 0.15元/次
- **计费方式**: 按调用次数计费，不成功不计费

### 成本控制建议

1. **图片预处理**
   - 压缩图片大小（建议<2MB）
   - 调整图片分辨率到合适尺寸
   - 避免上传过大的图片

2. **缓存策略**
   - 对相同图片的处理结果进行缓存
   - 避免重复调用API
   - 设置合理的缓存过期时间

3. **错误处理**
   - 添加重试机制，但要限制重试次数
   - 对明显无效的图片进行预检查
   - 记录调用日志，便于分析使用情况

## 🔒 安全配置

### 1. 密钥安全

- ✅ **正确做法**: 密钥存储在服务器端PHP文件中
- ❌ **错误做法**: 将密钥写在小程序前端代码中
- 🔄 **定期更换**: 建议每3-6个月更换一次密钥

### 2. 接口安全

```php
// 添加IP白名单（可选）
$allowedIPs = ['your_server_ip', 'another_ip'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowedIPs)) {
    http_response_code(403);
    exit('Access Denied');
}

// 添加请求频率限制（可选）
$maxRequestsPerMinute = 60;
// 实现频率限制逻辑...
```

### 3. 数据安全

- 图片数据仅用于API调用，不存储在服务器
- 处理完成后立即清理临时数据
- 使用HTTPS协议传输数据

## 🚀 性能优化

### 1. 图片优化

```javascript
// 压缩图片
wx.compressImage({
  src: imagePath,
  quality: 80, // 压缩质量80%
  success: (res) => {
    // 使用压缩后的图片
  }
})
```

### 2. 请求优化

```php
// 设置合理的超时时间
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
```

### 3. 错误重试

```javascript
// 添加重试机制
async function callAPIWithRetry(data, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await callAPI(data);
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
    }
  }
}
```

## 🐛 常见问题

### 1. API调用失败

**问题**: 返回"签名错误"
**解决**: 检查SecretId和SecretKey是否正确，确保没有多余的空格

**问题**: 返回"图片格式错误"
**解决**: 确保base64编码正确，图片格式为JPG或PNG

### 2. 网络请求失败

**问题**: 小程序提示"不在以下 request 合法域名列表中"
**解决**: 在微信小程序后台配置request合法域名

**问题**: PHP curl请求失败
**解决**: 检查服务器是否支持curl，是否能访问外网

### 3. 处理效果不佳

**问题**: 人像边缘不够精确
**解决**: 
- 使用清晰度更高的图片
- 确保人像在图片中占比适中
- 背景尽量简单，避免复杂背景

**问题**: 处理速度慢
**解决**:
- 压缩图片大小
- 选择就近的API地域
- 优化网络环境

## 📞 技术支持

如果遇到问题，可以：

1. **查看腾讯云文档**: https://cloud.tencent.com/document/product/865
2. **提交工单**: 腾讯云控制台 → 费用 → 工单管理
3. **社区支持**: 腾讯云开发者社区

## 📝 更新日志

- **v1.0.0**: 初始版本，支持基础人像分割功能
- **v1.1.0**: 增加错误重试机制和性能优化
- **v1.2.0**: 添加图片预处理和缓存功能
