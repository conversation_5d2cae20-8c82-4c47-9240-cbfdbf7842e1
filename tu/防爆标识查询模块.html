<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>防爆标识查询模块</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            padding: 0;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%,
                rgba(255, 255, 255, 0.3) 0%,
                rgba(255, 255, 255, 0.1) 50%,
                rgba(255, 255, 255, 0.05) 100%);
            pointer-events: none;
            z-index: 0;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            margin: 20px auto;
            background: #000;
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }

        .status-bar {
            height: 44px;
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(20px);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            font-size: 14px;
            font-weight: 600;
            color: #000;
        }

        .time {
            font-size: 15px;
            font-weight: 600;
        }

        .battery-info {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .signal-bars {
            display: flex;
            gap: 2px;
        }

        .bar {
            width: 3px;
            background: #000;
            border-radius: 1px;
        }

        .bar:nth-child(1) { height: 4px; }
        .bar:nth-child(2) { height: 6px; }
        .bar:nth-child(3) { height: 8px; }
        .bar:nth-child(4) { height: 10px; }

        .battery {
            width: 24px;
            height: 12px;
            border: 1px solid #000;
            border-radius: 2px;
            position: relative;
        }

        .battery::after {
            content: '';
            position: absolute;
            right: -3px;
            top: 3px;
            width: 2px;
            height: 6px;
            background: #000;
            border-radius: 0 1px 1px 0;
        }

        .battery-fill {
            width: 80%;
            height: 100%;
            background: #000;
            border-radius: 1px;
        }

        .content {
            height: calc(100% - 44px - 83px);
            overflow-y: auto;
            padding: 16px;
            position: relative;
            z-index: 1;
        }

        .header {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 8px 24px rgba(71, 85, 105, 0.08);
        }

        .header h1 {
            font-size: 24px;
            font-weight: 600;
            color: rgba(30, 41, 59, 0.9);
            margin-bottom: 8px;
        }

        .header p {
            font-size: 14px;
            color: rgba(100, 116, 139, 0.8);
        }

        .search-section {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 24px rgba(71, 85, 105, 0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: rgba(30, 41, 59, 0.9);
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .input-group {
            margin-bottom: 16px;
        }

        .input-label {
            font-size: 14px;
            color: rgba(71, 85, 105, 0.8);
            margin-bottom: 8px;
            display: block;
        }

        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 12px;
            font-size: 16px;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            transition: all 0.2s ease;
        }

        .input-field:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(145deg, #3b82f6, #2563eb);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .search-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
        }

        .result-section {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 24px rgba(71, 85, 105, 0.08);
        }

        .result-item {
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #ef4444;
        }

        .result-item:last-child {
            margin-bottom: 0;
        }

        .result-label {
            font-size: 14px;
            font-weight: 600;
            color: #ef4444;
            margin-bottom: 4px;
        }

        .result-value {
            font-size: 16px;
            color: rgba(30, 41, 59, 0.9);
            font-weight: 500;
        }

        .quick-reference {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 24px rgba(71, 85, 105, 0.08);
        }

        .reference-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .reference-item {
            background: rgba(248, 250, 252, 0.8);
            border-radius: 12px;
            padding: 12px;
            text-align: center;
            border: 1px solid rgba(226, 232, 240, 0.5);
        }

        .reference-code {
            font-size: 14px;
            font-weight: 600;
            color: #ef4444;
            margin-bottom: 4px;
        }

        .reference-desc {
            font-size: 12px;
            color: rgba(71, 85, 105, 0.8);
            line-height: 1.3;
        }

        .tab-bar {
            height: 83px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: space-around;
            padding-bottom: 34px;
        }

        .tab-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            color: #8e8e93;
            font-size: 10px;
            text-decoration: none;
        }

        .tab-item.active {
            color: #007aff;
        }

        .tab-icon {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .example-section {
            background: rgba(255, 255, 255, 0.6);
            backdrop-filter: blur(25px);
            border: 1px solid rgba(226, 232, 240, 0.6);
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 24px rgba(71, 85, 105, 0.08);
        }

        .example-item {
            background: rgba(239, 246, 255, 0.8);
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            border-left: 4px solid #3b82f6;
        }

        .example-code {
            font-size: 16px;
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
        }

        .example-desc {
            font-size: 14px;
            color: rgba(71, 85, 105, 0.8);
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <div class="phone-container">
        <div class="screen">
            <!-- 状态栏 -->
            <div class="status-bar">
                <div class="time">9:41</div>
                <div class="battery-info">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <i class="fas fa-wifi" style="font-size: 12px;"></i>
                    <div class="battery">
                        <div class="battery-fill"></div>
                    </div>
                </div>
            </div>

            <!-- 内容区域 -->
            <div class="content">
                <!-- 页面标题 -->
                <div class="header">
                    <h1><i class="fas fa-shield-alt" style="color: #ef4444; margin-right: 8px;"></i>防爆标识查询</h1>
                    <p>输入防爆型号自动解读含义，或查看标准参考</p>
                </div>

                <!-- 搜索区域 -->
                <div class="search-section">
                    <div class="section-title">
                        <i class="fas fa-search" style="color: #3b82f6;"></i>
                        型号解读
                    </div>
                    <div class="input-group">
                        <label class="input-label">请输入防爆标识型号</label>
                        <input type="text" class="input-field" placeholder="例如：Ex d IIB T4 Gb" value="Ex d IIB T4 Gb">
                    </div>
                    <button class="search-btn">
                        <i class="fas fa-search" style="margin-right: 8px;"></i>
                        解读标识
                    </button>
                </div>

                <!-- 解读结果 -->
                <div class="result-section">
                    <div class="section-title">
                        <i class="fas fa-clipboard-list" style="color: #ef4444;"></i>
                        解读结果
                    </div>
                    <div class="result-item">
                        <div class="result-label">Ex - 防爆标识</div>
                        <div class="result-value">表示该设备符合防爆标准</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">d - 隔爆型</div>
                        <div class="result-value">爆炸气体在壳体内被隔离</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">IIB - 气体组别</div>
                        <div class="result-value">适用于乙烯等中等危险性气体</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">T4 - 温度组别</div>
                        <div class="result-value">设备表面温度 ≤135℃</div>
                    </div>
                    <div class="result-item">
                        <div class="result-label">Gb - 保护级别</div>
                        <div class="result-value">适用于1区气体环境</div>
                    </div>
                </div>

                <!-- 常用示例 -->
                <div class="example-section">
                    <div class="section-title">
                        <i class="fas fa-lightbulb" style="color: #f59e0b;"></i>
                        常用示例
                    </div>
                    <div class="example-item">
                        <div class="example-code">Ex ia IIC T6 Ga</div>
                        <div class="example-desc">本质安全型，适用于氢气等高危险气体，表面温度≤85℃，0区环境</div>
                    </div>
                    <div class="example-item">
                        <div class="example-code">Ex e IIA T3 Gc</div>
                        <div class="example-desc">增安型，适用于丙烷等低危险气体，表面温度≤200℃，2区环境</div>
                    </div>
                </div>

                <!-- 快速参考 -->
                <div class="quick-reference">
                    <div class="section-title">
                        <i class="fas fa-book" style="color: #10b981;"></i>
                        快速参考
                    </div>
                    <div class="reference-grid">
                        <div class="reference-item">
                            <div class="reference-code">d</div>
                            <div class="reference-desc">隔爆型</div>
                        </div>
                        <div class="reference-item">
                            <div class="reference-code">e</div>
                            <div class="reference-desc">增安型</div>
                        </div>
                        <div class="reference-item">
                            <div class="reference-code">ia</div>
                            <div class="reference-desc">本安型</div>
                        </div>
                        <div class="reference-item">
                            <div class="reference-code">p</div>
                            <div class="reference-desc">正压型</div>
                        </div>
                        <div class="reference-item">
                            <div class="reference-code">IIA</div>
                            <div class="reference-desc">丙烷类</div>
                        </div>
                        <div class="reference-item">
                            <div class="reference-code">IIB</div>
                            <div class="reference-desc">乙烯类</div>
                        </div>
                        <div class="reference-item">
                            <div class="reference-code">IIC</div>
                            <div class="reference-desc">氢气类</div>
                        </div>
                        <div class="reference-item">
                            <div class="reference-code">T1-T6</div>
                            <div class="reference-desc">温度等级</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 底部导航 -->
            <div class="tab-bar">
                <a href="#" class="tab-item">
                    <div class="tab-icon">🏠</div>
                    <span>主页</span>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">🧪</div>
                    <span>化验</span>
                </a>
                <a href="#" class="tab-item">
                    <div class="tab-icon">💊</div>
                    <span>药剂</span>
                </a>
                <a href="#" class="tab-item active">
                    <div class="tab-icon">🚢</div>
                    <span>出海</span>
                </a>
            </div>
        </div>
    </div>
</body>
</html>
