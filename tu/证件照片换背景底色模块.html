<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>证件照片换背景底色 - 功能原型图</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(180deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .prototype-container {
            max-width: 375px;
            margin: 0 auto;
            background: #fff;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 20px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            width: 20px;
            height: 20px;
            border-left: 2px solid rgba(255,255,255,0.8);
            border-top: 2px solid rgba(255,255,255,0.8);
            transform: rotate(-45deg);
        }

        .header h1 {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .main-content {
            padding: 30px 20px;
        }

        .upload-section {
            text-align: center;
            margin-bottom: 20px;
        }

        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 10px;
            padding: 25px 15px;
            background: #f9fafb;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .upload-icon {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }

        .upload-text {
            font-size: 14px;
            color: #374151;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .upload-hint {
            font-size: 11px;
            color: #6b7280;
        }

        .preview-section {
            margin-bottom: 30px;
        }

        .preview-container {
            display: flex;
            gap: 15px;
            justify-content: center;
            align-items: flex-start;
        }

        .preview-item {
            flex: 1;
            text-align: center;
        }

        .preview-box {
            width: 120px;
            height: 160px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .preview-box.original {
            background: linear-gradient(45deg, #ff6b6b, #ffa500);
        }

        .preview-box.processed {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
        }

        .preview-label {
            font-size: 14px;
            color: #374151;
            font-weight: 500;
        }

        .arrow {
            font-size: 24px;
            color: #9ca3af;
            align-self: center;
            margin-top: 60px;
        }

        .size-selection {
            margin-bottom: 20px;
        }

        .size-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .size-option {
            display: flex;
            align-items: center;
            padding: 8px 10px;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            background: #f9fafb;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .size-option.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .size-option:hover {
            border-color: #9ca3af;
        }

        .size-preview {
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
        }

        .size-rect {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 2px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .size-info {
            flex: 1;
        }

        .size-name {
            font-size: 12px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 1px;
        }

        .size-detail {
            font-size: 10px;
            color: #6b7280;
        }

        .color-selection {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin-bottom: 15px;
        }

        .color-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .color-option {
            aspect-ratio: 1;
            border-radius: 8px;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .color-option.selected {
            border-color: #667eea;
            transform: scale(1.1);
        }

        .color-option.red { background: #dc2626; }
        .color-option.blue { background: #2563eb; }
        .color-option.white { background: #ffffff; border: 1px solid #e5e7eb; }
        .color-option.blue-white {
            background: linear-gradient(180deg, #2563eb 0%, #3b82f6 30%, #93c5fd 70%, #ffffff 100%);
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .color-label {
            font-size: 12px;
            color: #6b7280;
            text-align: center;
            margin-top: 5px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
        }

        .btn {
            flex: 1;
            padding: 14px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
            border: 1px solid #d1d5db;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .features-list {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            margin-top: 20px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            color: #374151;
        }

        .feature-item:last-child {
            margin-bottom: 0;
        }

        .feature-icon {
            width: 20px;
            height: 20px;
            background: #10b981;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .status-text {
            text-align: center;
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="prototype-container">
        <!-- 头部导航 -->
        <div class="header">
            <h1>证件照换背景</h1>
            <p>智能抠图，一键换色</p>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 上传区域 -->
            <div class="upload-section">
                <div class="upload-area" onclick="simulateUpload()">
                    <div class="upload-icon">📷</div>
                    <div class="upload-text">点击上传证件照</div>
                    <div class="upload-hint">支持 JPG、PNG 格式，建议尺寸 295×413</div>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="preview-section">
                <div class="preview-container">
                    <div class="preview-item">
                        <div class="preview-box original">
                            <span style="color: white; font-size: 14px;">原图</span>
                        </div>
                        <div class="preview-label">原始照片</div>
                    </div>
                    <div class="arrow">→</div>
                    <div class="preview-item">
                        <div class="preview-box processed">
                            <span style="color: white; font-size: 14px;">效果</span>
                        </div>
                        <div class="preview-label">处理结果</div>
                    </div>
                </div>
            </div>

            <!-- 背景色选择 -->
            <div class="color-selection">
                <div class="section-title">选择背景色</div>
                <div class="color-grid">
                    <div>
                        <div class="color-option red selected" onclick="selectColor(this)"></div>
                        <div class="color-label">红色</div>
                    </div>
                    <div>
                        <div class="color-option blue" onclick="selectColor(this)"></div>
                        <div class="color-label">蓝色</div>
                    </div>
                    <div>
                        <div class="color-option white" onclick="selectColor(this)"></div>
                        <div class="color-label">白色</div>
                    </div>
                    <div>
                        <div class="color-option blue-white" onclick="selectColor(this)"></div>
                        <div class="color-label">蓝白渐变</div>
                    </div>
                </div>
            </div>

            <!-- 尺寸选择 -->
            <div class="size-selection">
                <div class="section-title">选择照片尺寸</div>
                <div class="size-grid">
                    <div class="size-option selected" onclick="selectSize(this)" data-size="1inch">
                        <div class="size-preview">
                            <div class="size-rect" style="width: 25px; height: 35px;"></div>
                        </div>
                        <div class="size-info">
                            <div class="size-name">一寸照</div>
                            <div class="size-detail">25×35mm</div>
                        </div>
                    </div>
                    <div class="size-option" onclick="selectSize(this)" data-size="2inch">
                        <div class="size-preview">
                            <div class="size-rect" style="width: 30px; height: 42px;"></div>
                        </div>
                        <div class="size-info">
                            <div class="size-name">二寸照</div>
                            <div class="size-detail">35×49mm</div>
                        </div>
                    </div>
                    <div class="size-option" onclick="selectSize(this)" data-size="small2inch">
                        <div class="size-preview">
                            <div class="size-rect" style="width: 30px; height: 39px;"></div>
                        </div>
                        <div class="size-info">
                            <div class="size-name">小二寸</div>
                            <div class="size-detail">35×45mm</div>
                        </div>
                    </div>
                    <div class="size-option" onclick="selectSize(this)" data-size="passport">
                        <div class="size-preview">
                            <div class="size-rect" style="width: 33px; height: 40px;"></div>
                        </div>
                        <div class="size-info">
                            <div class="size-name">护照照片</div>
                            <div class="size-detail">33×48mm</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="status-text" id="statusText">等待上传照片...</div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button class="btn btn-secondary" onclick="resetAll()">重新选择</button>
                <button class="btn btn-primary" onclick="processImage()">开始处理</button>
            </div>

            <!-- 功能特点 -->
            <div class="features-list">
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>AI智能抠图，精准识别人像边缘</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>支持红、蓝、白、蓝白渐变等证件照背景</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>多种尺寸：一寸、二寸、小二寸、护照照片</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>高清输出，保持原图质量</span>
                </div>
                <div class="feature-item">
                    <div class="feature-icon">✓</div>
                    <span>本地处理，保护隐私安全</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function selectSize(element) {
            // 移除所有尺寸选中状态
            document.querySelectorAll('.size-option').forEach(el => {
                el.classList.remove('selected');
            });
            // 添加选中状态
            element.classList.add('selected');

            // 更新状态文本
            const sizeName = element.querySelector('.size-name').textContent;
            const sizeDetail = element.querySelector('.size-detail').textContent;
            document.getElementById('statusText').textContent = `已选择${sizeName} (${sizeDetail})`;
        }

        function selectColor(element) {
            // 移除所有选中状态
            document.querySelectorAll('.color-option').forEach(el => {
                el.classList.remove('selected');
            });
            // 添加选中状态
            element.classList.add('selected');
        }

        function simulateUpload() {
            document.getElementById('statusText').textContent = '正在上传照片...';
            const progressFill = document.getElementById('progressFill');
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                progressFill.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    document.getElementById('statusText').textContent = '照片上传完成，请选择背景色';
                }
            }, 100);
        }

        function processImage() {
            document.getElementById('statusText').textContent = '正在处理照片...';
            const progressFill = document.getElementById('progressFill');
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 5;
                progressFill.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    document.getElementById('statusText').textContent = '处理完成！可以保存照片了';
                }
            }, 80);
        }

        function resetAll() {
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('statusText').textContent = '等待上传照片...';
        }
    </script>
</body>
</html>
